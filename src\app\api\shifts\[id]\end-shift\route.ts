import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getCurrentUser } from '@/lib/middleware';
import { UserRole } from '@prisma/client';
import { hasRequiredRole } from '@/lib/auth';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    if (!hasRequiredRole(user as { id: string; role: UserRole; }, [UserRole.Admin, UserRole.CrewChief])) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const { id: shiftId } = params;
    const { workerId } = await request.json();

    if (!workerId) {
      return NextResponse.json({ error: 'Worker ID is required' }, { status: 400 });
    }

    const shift = await prisma.shift.findUnique({
      where: { id: shiftId },
      include: { assignedPersonnel: true },
    });

    if (!shift) {
      return NextResponse.json({ error: 'Shift not found' }, { status: 404 });
    }

    const isWorkerAssigned = shift.assignedPersonnel.some(p => p.id === workerId);
    if (!isWorkerAssigned) {
      return NextResponse.json({ error: 'Worker not assigned to this shift' }, { status: 400 });
    }

    const now = new Date();

    const activeEntry = await prisma.timeEntry.findFirst({
      where: {
        assignedPersonnelId: workerId,
        isActive: true,
      },
    });

    if (activeEntry) {
      await prisma.timeEntry.update({
        where: { id: activeEntry.id },
        data: {
          clockOut: now,
          isActive: false,
        },
      });
    }

    return NextResponse.json({
      success: true,
      message: 'Employee shift ended successfully',
    });
  } catch (error) {
    console.error('Error ending employee shift:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

import { PrismaClient, UserRole } from '@prisma/client';
import bcrypt from 'bcryptjs';
import { faker } from '@faker-js/faker';

const prisma = new PrismaClient();

async function main() {
  console.log('Starting database seeding...');

  // Clear existing data
  await prisma.timeEntry.deleteMany({});
  await prisma.assignedPersonnel.deleteMany({});
  await prisma.workerRequirement.deleteMany({});
  await prisma.timesheet.deleteMany({});
  await prisma.shift.deleteMany({});
  await prisma.job.deleteMany({});
  await prisma.passwordResetToken.deleteMany({});
  await prisma.user.deleteMany({});
  await prisma.company.deleteMany({});

  console.log('Cleared existing data.');

  // --- Hash a common password ---
  const password = 'password123';
  const hashedPassword = await bcrypt.hash(password, 12);
  console.log(`Using common password: "${password}"`);

  // --- Create Users for Each Role ---
  const adminUser = await prisma.user.create({
    data: {
      name: 'Admin User',
      email: '<EMAIL>',
      passwordHash: hashedPassword,
      role: UserRole.Admin,
    },
  });
  console.log('Created Admin User: <EMAIL>');

  const managerUser = await prisma.user.create({
    data: {
      name: 'Manager User',
      email: '<EMAIL>',
      passwordHash: hashedPassword,
      role: UserRole.Admin,
    },
  });
  console.log('Created Manager User: <EMAIL>');

  const crewChiefUser = await prisma.user.create({
    data: {
      name: 'Crew Chief User',
      email: '<EMAIL>',
      passwordHash: hashedPassword,
      role: UserRole.CrewChief,
    },
  });
  console.log('Created Crew Chief User: <EMAIL>');

  // --- Create Employees ---
  const employees = [];
  for (let i = 0; i < 10; i++) {
    const employee = await prisma.user.create({
      data: {
        name: faker.person.fullName(),
        email: faker.internet.email(),
        passwordHash: hashedPassword,
        role: UserRole.Employee,
      },
    });
    employees.push(employee);
    console.log(`Created Employee: ${employee.email}`);
  }

  // --- Create Companies, Jobs, Shifts, and Assignments ---
  for (let i = 0; i < 3; i++) {
    const company = await prisma.company.create({
      data: {
        name: faker.company.name(),
        address: faker.location.streetAddress(),
        phone: faker.phone.number(),
        email: faker.internet.email(),
      },
    });
    console.log(`Created company: ${company.name}`);

    await prisma.user.create({
        data: {
          name: faker.person.fullName(),
          email: faker.internet.email(),
          passwordHash: hashedPassword,
          role: UserRole.CompanyUser,
          companyId: company.id,
        },
      });

    for (let j = 0; j < 3; j++) {
      const job = await prisma.job.create({
        data: {
          name: faker.commerce.productName(),
          description: faker.lorem.sentence(),
          status: 'Pending',
          companyId: company.id,
        },
      });
      console.log(`Created job: ${job.name}`);

      for (let k = 0; k < 3; k++) {
        const shift = await prisma.shift.create({
          data: {
            date: faker.date.future(),
            startTime: faker.date.future(),
            endTime: faker.date.future(),
            jobId: job.id,
            requestedWorkers: 5,
          },
        });
        console.log(`Created shift for job: ${job.name}`);

        // Assign random employees to the shift
        const assignedEmployees = faker.helpers.arrayElements(employees, 3);
        for (const employee of assignedEmployees) {
          await prisma.assignedPersonnel.create({
            data: {
              shiftId: shift.id,
              userId: employee.id,
            },
          });
          console.log(`Assigned ${employee.name} to shift for job: ${job.name}`);
        }
      }
    }
  }

  console.log('Database seeding completed successfully!');
}

main()
  .catch((e) => {
    console.error('Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });

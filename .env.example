# HoliTime Environment Configuration Template
# Copy this file to .env and fill in your actual values
# DO NOT commit .env files to version control

# =============================================================================
# CORE APPLICATION SETTINGS
# =============================================================================

# Application environment (development, production, test)
NODE_ENV="development"

# Disable Next.js telemetry
NEXT_TELEMETRY_DISABLED="1"

# Application URL - MUST match your deployment URL exactly
# Development: http://localhost:3000
# Production: https://your-domain.com
NEXTAUTH_URL="http://localhost:3000"

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# Primary database connection string
# Format: postgresql://username:password@host:port/database?options
# Local example: postgresql://postgres:password@localhost:5432/holitime
# Cloud example: postgresql://user:pass@host:port/db?sslmode=require
DATABASE_URL="postgresql://postgres:password@localhost:5432/holitime"

# Direct database connection (used for migrations and admin operations)
# Usually the same as DATABASE_URL unless using connection pooling
DATABASE_URL_DIRECT="postgresql://postgres:password@localhost:5432/holitime"

# =============================================================================
# AUTHENTICATION & SECURITY
# =============================================================================

# NextAuth.js secret key - Generate with: openssl rand -base64 32
# MUST be different for each environment
NEXTAUTH_SECRET="your-nextauth-secret-key-here"

# JWT secret for custom token operations
# Can be the same as NEXTAUTH_SECRET or generate separately
JWT_SECRET="your-jwt-secret-key-here"

# =============================================================================
# GOOGLE OAUTH & API CONFIGURATION
# =============================================================================

# Google OAuth credentials from Google Cloud Console
# Required for Google sign-in and API access
GOOGLE_CLIENT_ID="your-client-id.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET="your-client-secret"

# Google API key for Sheets/Drive integration
# Enable Google Sheets API and Google Drive API in Google Cloud Console
GOOGLE_API_KEY="your-google-api-key"

# Google AI API key for Gemini integration (optional)
GOOGLE_AI_API_KEY="your-google-ai-api-key"

# =============================================================================
# EMAIL CONFIGURATION (Optional but recommended)
# =============================================================================

# SMTP server settings for email notifications
# Gmail example (requires app password):
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"

# Alternative SMTP providers:
# SendGrid:
# SMTP_HOST="smtp.sendgrid.net"
# SMTP_PORT="587"
# SMTP_USER="apikey"
# SMTP_PASS="your-sendgrid-api-key"

# Outlook/Hotmail:
# SMTP_HOST="smtp-mail.outlook.com"
# SMTP_PORT="587"
# SMTP_USER="<EMAIL>"
# SMTP_PASS="your-password"

# =============================================================================
# REAL-TIME FEATURES (Optional)
# =============================================================================

# WebSocket URL for real-time updates
# Development: ws://localhost:3000/api/ws
# Production: wss://your-domain.com/api/ws
NEXT_PUBLIC_WS_URL="ws://localhost:3000/api/ws"

# =============================================================================
# FILE STORAGE (Optional - choose one)
# =============================================================================

# AWS S3 Configuration
# AWS_ACCESS_KEY_ID="your-aws-access-key"
# AWS_SECRET_ACCESS_KEY="your-aws-secret-key"
# AWS_REGION="us-east-1"
# AWS_S3_BUCKET="your-bucket-name"

# Google Cloud Storage Configuration
# GOOGLE_CLOUD_PROJECT_ID="your-project-id"
# GOOGLE_CLOUD_STORAGE_BUCKET="your-bucket-name"

# =============================================================================
# MONITORING & ANALYTICS (Optional)
# =============================================================================

# Sentry for error tracking
# SENTRY_DSN="your-sentry-dsn"

# Google Analytics
# NEXT_PUBLIC_GA_ID="G-XXXXXXXXXX"

# Application version for monitoring
# NEXT_PUBLIC_APP_VERSION="1.0.0"

# =============================================================================
# DEVELOPMENT & DEBUGGING (Development only)
# =============================================================================

# Enable debug logging in development
# DEBUG="true"

# Database query logging
# DATABASE_LOGGING="true"

# =============================================================================
# ADDITIONAL NOTES
# =============================================================================

# 1. Generate secure secrets:
#    openssl rand -base64 32

# 2. Google OAuth Setup:
#    - Go to Google Cloud Console
#    - Create OAuth 2.0 credentials
#    - Add redirect URIs:
#      Development: http://localhost:3000/api/auth/callback/google
#      Production: https://your-domain.com/api/auth/callback/google

# 3. Database Setup:
#    - Create a PostgreSQL database
#    - Run: npx prisma migrate dev
#    - Seed data: npx prisma db seed

# 4. Email Setup (Gmail):
#    - Enable 2-factor authentication
#    - Generate app password
#    - Use app password in SMTP_PASS

# 5. Security Checklist:
#    - Use different secrets for each environment
#    - Enable SSL for database connections in production
#    - Use HTTPS for NEXTAUTH_URL in production
#    - Never commit .env files to version control
#    - Rotate secrets regularly

# 6. Validation:
#    - Test configuration: npm run env:check
#    - Debug endpoint: GET /api/debug/environment (development only)

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/middleware';
import { prisma } from '@/lib/prisma';
import { UserRole } from '@prisma/client';

export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get today's date in YYYY-MM-DD format
    const today = new Date().toISOString().split('T')[0];

    // Simplified, fast query - get basic shift info only
    const result = await prisma.shift.findMany({
      where: {
        date: {
          gte: new Date(today),
          lt: new Date(new Date(today).getTime() + 24 * 60 * 60 * 1000),
        },
      },
      include: {
        job: {
          include: {
            company: true,
          },
        },
        assignedPersonnel: {
          where: { roleCode: 'CC' },
          include: { user: true },
        },
      },
      orderBy: { startTime: 'asc' },
    });

    // Get assigned personnel counts in a separate, simple query
    const assignedCounts = await prisma.assignedPersonnel.groupBy({
      by: ['shiftId'],
      where: {
        shiftId: { in: result.map(s => s.id) },
        isPlaceholder: false,
      },
      _count: {
        _all: true,
      },
    });
    const assignedCountsMap = new Map(assignedCounts.map(c => [c.shiftId, c._count._all]));

    // Transform the data to match the expected format (simplified for performance)
    const shifts = result.map(shift => ({
      id: shift.id,
      timesheetId: '', // Load on demand for performance
      jobId: shift.jobId,
      jobName: shift.job.name,
      clientName: shift.job.company.name,
      date: shift.date,
      startTime: shift.startTime,
      endTime: shift.endTime,
      location: shift.job.company.name,
      requestedWorkers: shift.requestedWorkers || 1,
      assignedCount: assignedCountsMap.get(shift.id) || 0,
      crewChiefId: shift.assignedPersonnel[0]?.userId,
      crewChiefName: shift.assignedPersonnel[0]?.user.name,
      crewChiefAvatar: '', // Simplified for performance
      assignedPersonnel: [], // Load on demand for performance
      status: shift.status,
      timesheetStatus: 'Pending Finalization', // Default status
    }));

    // Filter based on user role (simplified for performance)
    let filteredShifts = shifts;

    if (user.role === UserRole.CrewChief) {
      filteredShifts = shifts.filter(shift => shift.crewChiefId === user.id);
    } else if (user.role === UserRole.Staff || user.role === UserRole.Employee) {
      if (shifts.length > 0) {
        const employeeShiftIds = await prisma.assignedPersonnel.findMany({
          where: {
            userId: user.id,
            shiftId: { in: shifts.map(s => s.id) },
          },
          select: { shiftId: true },
        });
        const employeeShiftIdSet = new Set(employeeShiftIds.map(s => s.shiftId));
        filteredShifts = shifts.filter(shift => employeeShiftIdSet.has(shift.id));
      } else {
        filteredShifts = [];
      }
    }
    // Admin and Customer users see all shifts
    if (user.role === UserRole.CompanyUser && user.companyId) {
      const resultWithCompany = await prisma.shift.findMany({
        where: {
          id: { in: shifts.map(s => s.id) },
          job: {
            companyId: user.companyId,
          },
        },
        select: { id: true },
      });
      const companyShiftIdSet = new Set(resultWithCompany.map(s => s.id));
      filteredShifts = shifts.filter(shift => companyShiftIdSet.has(shift.id));
    }

    return NextResponse.json({
      success: true,
      shifts: filteredShifts,
    });

  } catch (error) {
    console.error('Error getting today\'s shifts:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

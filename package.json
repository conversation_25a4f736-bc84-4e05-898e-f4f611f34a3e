{"name": "nextn", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "genkit:dev": "genkit start -- tsx src/ai/dev.ts", "genkit:watch": "genkit start -- tsx --watch src/ai/dev.ts", "build": "next build", "start": "next start", "lint": "next lint", "typecheck": "npx tsc --noEmit", "migrate": "npx prisma migrate dev --schema=./prisma/schema.prisma", "migrate:prod": "npx prisma migrate deploy --schema=./prisma/schema.prisma", "db:setup": "npm run migrate", "deploy": "powershell -ExecutionPolicy Bypass -File handsonlabor-website/deploy-cloud-run.ps1 -ProjectId holitime-465520", "deploy:bash": "./deploy.sh", "studio": "npx prisma studio", "fix-jobs": "cross-env NODE_TLS_REJECT_UNAUTHORIZED=0 tsx scripts/fix-jobs.ts", "import-new-shifts": "tsx scripts/import-new-shifts.ts", "db:seed": "npx prisma db seed"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "dependencies": {"@auth/pg-adapter": "^1.10.0", "@faker-js/faker": "^9.9.0", "@genkit-ai/googleai": "^1.14.0", "@genkit-ai/next": "^1.14.0", "@google-cloud/cloud-sql-connector": "^1.8.2", "@google/generative-ai": "^0.11.0", "@hookform/resolvers": "^5.1.1", "@mantine/core": "^8.1.3", "@mantine/form": "^8.1.3", "@mantine/hooks": "^8.1.3", "@mantine/notifications": "^8.1.3", "@prisma/client": "^6.12.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@react-native-community/push-notification-ios": "^1.11.0", "@tanstack/react-query": "^5.83.0", "@types/bcryptjs": "^3.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/pg": "^8.15.4", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^17.2.0", "dotenv-cli": "^8.0.0", "eslint": "^8.0.0", "firebase": "^11.10.0", "genkit": "^1.14.0", "go": "^3.0.1", "googleapis": "^152.0.0", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.525.0", "next": "14.2.5", "next-auth": "^4.24.7", "patch-package": "^8.0.0", "pdf-lib": "^1.17.1", "pg": "^8.16.3", "postcss-preset-mantine": "^1.18.0", "psql": "^0.0.1", "react": "^18.3.1", "react-day-picker": "^9.8.0", "react-dom": "^18.3.1", "react-hook-form": "^7.60.0", "react-pdf": "^10.0.1", "react-signature-canvas": "^1.1.0-alpha.2", "react-toastify": "^11.0.5", "recharts": "^3.1.0", "sharp": "^0.34.3", "tailwind-merge": "^3.3.1", "xlsx": "^0.18.5", "zod": "^3.25.76", "zustand": "^5.0.6"}, "devDependencies": {"@types/csv-parse": "^1.1.12", "@types/node": "^20.11.0", "@types/react": "18.3.3", "@types/react-dom": "^18.3.0", "@types/react-native": "^0.72.8", "@types/react-native-push-notification": "^8.1.4", "@types/react-signature-canvas": "^1.0.7", "@types/tailwindcss": "^3.0.11", "@typescript-eslint/eslint-plugin": "^8.37.0", "cross-env": "^7.0.3", "csv-parse": "^6.0.0", "eslint-config-next": "^14.2.5", "genkit-cli": "^1.14.0", "postcss": "^8.4.30", "prisma": "^6.12.0", "tailwindcss": "^4.1.11", "tsx": "^4.20.3", "typescript": "^5.8.3"}}
-- Add missing columns to jobs table (without enum for now)
ALTER TABLE "jobs" ADD COLUMN IF NOT EXISTS "startDate" TIMESTAMP(3);
ALTER TABLE "jobs" ADD COLUMN IF NOT EXISTS "endDate" TIMESTAMP(3);
ALTER TABLE "jobs" ADD COLUMN IF NOT EXISTS "location" TEXT;
ALTER TABLE "jobs" ADD COLUMN IF NOT EXISTS "budget" TEXT;
ALTER TABLE "jobs" ADD COLUMN IF NOT EXISTS "notes" TEXT;

-- Add missing columns to shifts table
ALTER TABLE "shifts" ADD COLUMN IF NOT EXISTS "location" TEXT;
ALTER TABLE "shifts" ADD COLUMN IF NOT EXISTS "description" TEXT;
ALTER TABLE "shifts" ADD COLUMN IF NOT EXISTS "requirements" TEXT;
ALTER TABLE "shifts" ADD COLUMN IF NOT EXISTS "notes" TEXT;

-- Create worker_requirements table
CREATE TABLE "worker_requirements" (
    "id" TEXT NOT NULL,
    "shiftId" TEXT NOT NULL,
    "roleCode" TEXT NOT NULL,
    "roleName" TEXT NOT NULL,
    "requiredCount" INTEGER NOT NULL DEFAULT 0,
    "color" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "worker_requirements_pkey" PRIMARY KEY ("id")
);

-- Create notifications table
CREATE TABLE "notifications" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "message" TEXT NOT NULL,
    "relatedTimesheetId" TEXT,
    "relatedShiftId" TEXT,
    "isRead" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "notifications_pkey" PRIMARY KEY ("id")
);

-- Create announcements table
CREATE TABLE "announcements" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "date" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdById" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "announcements_pkey" PRIMARY KEY ("id")
);

-- Add missing columns to timesheets table
ALTER TABLE "timesheets" ADD COLUMN IF NOT EXISTS "submittedBy" TEXT;
ALTER TABLE "timesheets" ADD COLUMN IF NOT EXISTS "clientApprovedAt" TIMESTAMP(3);
ALTER TABLE "timesheets" ADD COLUMN IF NOT EXISTS "clientApprovedBy" TEXT;
ALTER TABLE "timesheets" ADD COLUMN IF NOT EXISTS "clientSignature" TEXT;
ALTER TABLE "timesheets" ADD COLUMN IF NOT EXISTS "managerApprovedAt" TIMESTAMP(3);
ALTER TABLE "timesheets" ADD COLUMN IF NOT EXISTS "managerApprovedBy" TEXT;
ALTER TABLE "timesheets" ADD COLUMN IF NOT EXISTS "managerSignature" TEXT;
ALTER TABLE "timesheets" ADD COLUMN IF NOT EXISTS "rejectionReason" TEXT;

-- Make timesheets.shiftId unique
CREATE UNIQUE INDEX "timesheets_shiftId_key" ON "timesheets"("shiftId");

-- Create indexes for worker_requirements
CREATE INDEX "worker_requirements_shiftId_idx" ON "worker_requirements"("shiftId");
CREATE UNIQUE INDEX "worker_requirements_shiftId_roleCode_key" ON "worker_requirements"("shiftId", "roleCode");

-- Create indexes for notifications
CREATE INDEX "notifications_userId_idx" ON "notifications"("userId");
CREATE INDEX "notifications_isRead_idx" ON "notifications"("isRead");
CREATE INDEX "notifications_createdAt_idx" ON "notifications"("createdAt");

-- Create indexes for announcements
CREATE INDEX "announcements_createdById_idx" ON "announcements"("createdById");
CREATE INDEX "announcements_date_idx" ON "announcements"("date");

-- Add foreign key constraints
ALTER TABLE "worker_requirements" ADD CONSTRAINT "worker_requirements_shiftId_fkey" FOREIGN KEY ("shiftId") REFERENCES "shifts"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "notifications" ADD CONSTRAINT "notifications_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "announcements" ADD CONSTRAINT "announcements_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

# Stage 1: Build Environment
FROM node:22-slim AS builder
WORKDIR /app

# Install dependencies for building
RUN apt-get update && apt-get install -y openssl python3 make g++

# Copy package files and install dependencies
COPY package.json package-lock.json ./
RUN npm install --legacy-peer-deps

# Copy the rest of the application source code
COPY . .

# Generate Prisma client
RUN npx prisma generate

# Build the Next.js application
# Set dummy environment variables for build
ENV NEXTAUTH_SECRET="uivbienbuivenbivrenbiiinbrenbirenbiivrevnurnei"
ENV JWT_SECRET="uivbienbuivenbivrenujujubiiinbrenbirenbiivrevnurnei"
ENV DATABASE_URL="postgres://avnadmin:<EMAIL>:12297/defaultdb?sslmode=require"

RUN npm run build

# Stage 2: Production Environment
FROM node:22-slim AS production
WORKDIR /app

# Set production environment
ENV NODE_ENV=production

# Create a non-root user for security
RUN groupadd --gid 1001 nodejs && \
    useradd --uid 1001 --gid nodejs --shell /bin/bash --create-home nextjs

# Copy only the necessary files from the builder stage
COPY --from=builder /app/public ./public
COPY --from=builder /app/next.config.mjs ./
COPY --from=builder /app/package.json ./

# Copy the standalone output
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

# Copy Prisma schema and migration files for production migrations
COPY --from=builder /app/prisma ./prisma

# Switch to the non-root user
USER nextjs

# Expose the port the app will run on
EXPOSE 3000

# The command to start the application
CMD ["node", "server.js"]

{"name": "nextjs", "displayName": "Prisma NextJS", "icon": "images/icon.png", "repository": "https://github.com/prisma-labs/vscode-next", "description": "Automagicly add types to **nextjs** page functions i.e `getStaticSideProps`, `getServerSideProps` the returned types can not be automatically inferred and passed to the page. This extension also enables type safety in js code by leveraging JSDOCs", "version": "0.0.8", "publisher": "<PERSON><PERSON><PERSON>", "engines": {"vscode": "^1.51.0"}, "license": "MIT", "categories": ["Other"], "activationEvents": ["workspaceContains:**/*.prisma", "onCommand:WillLuke.nextjs.addTypes"], "main": "./dist/extension.js", "contributes": {"commands": [{"command": "WillLuke.nextjs.addTypes", "title": "Add Types to a NextJS Page", "category": "Prisma NextJS"}], "configuration": {"type": "object", "title": "Prisma NextJS", "properties": {"WillLuke.nextjs.root": {"scope": "resource", "type": "string", "description": "Option to set the path to a nextjs project"}, "WillLuke.nextjs.hasPrompted": {"scope": "resource", "type": "boolean", "description": "Hides Autotypes Prompt if it has been shown"}, "WillLuke.nextjs.addTypesOnSave": {"scope": "resource", "type": "boolean", "default": false, "description": "Automatically add NextJS page types on save"}, "WillLuke.debug": {"scope": "resource", "type": "boolean", "default": false, "description": "Debug the Extension"}}}}, "scripts": {"vscode:prepublish": "yarn run package", "compile": "webpack --devtool nosources-source-map --config ./build/node-extension.webpack.config.js", "watch": "webpack --watch --devtool nosources-source-map --info-verbosity verbose --config ./build/node-extension.webpack.config.js", "package": "webpack --mode production --config ./build/node-extension.webpack.config.js", "pkg": "vsce package", "test-compile": "tsc -p ./", "test-watch": "tsc -watch -p ./", "pretest": "yarn run test-compile", "test": "jest"}, "dependencies": {"debug": "^4.2.0", "ts-morph": "8.1.2", "yarn": "^1.22.22"}, "devDependencies": {"@types/debug": "^4.1.5", "@types/dedent": "0.7.0", "@types/glob": "^7.1.3", "@types/jest": "26.0.15", "@types/node": "^12.19.4", "@types/vscode": "^1.51.0", "@typescript-eslint/eslint-plugin": "^4.7.0", "@typescript-eslint/parser": "^4.7.0", "dedent": "0.7.0", "eslint": "^7.13.0", "fs-jetpack": "4.0.1", "glob": "^7.1.6", "jest": "26.6.3", "jest-snapshot-serializer-raw": "1.1.0", "tempy": "1.0.0", "ts-jest": "26.4.4", "ts-loader": "^8.0.11", "typescript": "^4.0.5", "vsce": "^1.81.1", "vscode-test": "^1.4.1", "webpack": "^4.44.1", "webpack-cli": "^3.3.12"}}
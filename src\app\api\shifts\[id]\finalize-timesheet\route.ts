import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getCurrentUser } from '@/lib/middleware'
import { UserRole } from '@prisma/client'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    if (![UserRole.Admin, UserRole.CrewChief].includes(user.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    const { id: shiftId } = await params;
    console.log(`Finalize timesheet request (UPDATED):`, { shiftId, userId: user.id })

    // Check if all workers have ended their shifts
    const activeWorkersResult = await prisma.timeEntry.count({
      where: {
        assignedPersonnel: {
          shiftId,
        },
        isActive: true,
      },
    });

    const activeCount = activeWorkersResult;

    if (activeCount > 0) {
      return NextResponse.json(
        { error: `Cannot finalize timesheet. ${activeCount} workers have not ended their shifts yet.` },
        { status: 400 }
      )
    }

    // Check if timesheet already exists
    const existingTimesheetResult = await prisma.timesheet.findUnique({
      where: { shiftId },
      select: { id: true },
    });

    let timesheetId;

    if (existingTimesheetResult) {
      timesheetId = existingTimesheetResult.id;
      await prisma.timesheet.update({
        where: { id: timesheetId },
        data: {
          status: 'Pending',
          // submitted_by: session.user.id,
          // submitted_at: new Date(),
        },
      });
    } else {
      const newTimesheetResult = await prisma.timesheet.create({
        data: {
          shiftId,
          status: 'Pending',
          // submitted_by: session.user.id,
          // submitted_at: new Date(),
        },
        select: { id: true },
      });
      timesheetId = newTimesheetResult.id;
    }

    // Update shift status
    await prisma.shift.update({
      where: { id: shiftId },
      data: { status: 'Pending' },
    });

    return NextResponse.json({
      success: true,
        message: 'Timesheet finalized and submitted for client approval',
        timesheetId
      })

    } catch (error) {
      console.error('Error finalizing timesheet:', error)
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      )
    }
}

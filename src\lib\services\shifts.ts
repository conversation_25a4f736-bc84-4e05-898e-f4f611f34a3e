import { prisma } from '../prisma';
import { Shift, ShiftStatus, Prisma, UserRole } from '@prisma/client';
import { User } from 'next-auth';
import { hasAnyRole } from '../auth';

export type ShiftWithDetails = Prisma.ShiftGetPayload<{
  include: {
    job: true;
    assignedPersonnel: {
      include: {
        user: {
          select: {
            id: true,
            name: true,
            avatarUrl: true,
          },
        },
      };
    };
  };
}>;

interface PaginatedShifts {
  shifts: ShiftWithDetails[];
  total: number;
  pages: number;
}

interface ShiftQueryOptions {
  page?: number;
  pageSize?: number;
  status?: ShiftStatus[];
  startDate?: string;
  endDate?: string;
  jobId?: string;
  companyId?: string;
}

export async function getShiftsByCrewChief(user: User): Promise<ShiftWithDetails[]> {
    const permissions = await prisma.crewChiefPermission.findMany({
        where: { userId: user.id, revokedAt: null },
    });

    const shiftIds = permissions
        .filter(p => p.permissionType === 'shift')
        .map(p => p.targetId);
    
    const jobIds = permissions
        .filter(p => p.permissionType === 'job')
        .map(p => p.targetId);

    const clientIds = permissions
        .filter(p => p.permissionType === 'client')
        .map(p => p.targetId);

    const shiftsFromJobs = await prisma.shift.findMany({
        where: { jobId: { in: jobIds } },
        include: {
            job: true,
            assignedPersonnel: {
                include: {
                    user: {
                        select: {
                            id: true,
                            name: true,
                            avatarUrl: true,
                        },
                    },
                },
            },
        },
    });

    const shiftsFromClients = await prisma.shift.findMany({
        where: { job: { companyId: { in: clientIds } } },
        include: {
            job: true,
            assignedPersonnel: {
                include: {
                    user: {
                        select: {
                            id: true,
                            name: true,
                            avatarUrl: true,
                        },
                    },
                },
            },
        },
    });

    const directShifts = await prisma.shift.findMany({
        where: { id: { in: shiftIds } },
        include: {
            job: true,
            assignedPersonnel: {
                include: {
                    user: {
                        select: {
                            id: true,
                            name: true,
                            avatarUrl: true,
                        },
                    },
                },
            },
        },
    });

    const allShifts = [...directShifts, ...shiftsFromJobs, ...shiftsFromClients];
    // Remove duplicates
    return allShifts.filter((shift, index, self) =>
        index === self.findIndex((s) => (
            s.id === shift.id
        ))
    );
}

export async function getAllShifts(
  user: User,
  options: ShiftQueryOptions = {}
): Promise<PaginatedShifts> {
  const {
    page = 1,
    pageSize = 50,
    status,
    startDate,
    endDate,
    jobId,
    companyId,
  } = options;

  const where: Prisma.ShiftWhereInput = {};
  if (status?.length) where.status = { in: status };
  if (startDate || endDate) {
    where.date = {};
    if (startDate) {
      where.date.gte = new Date(startDate);
    }
    if (endDate) {
      where.date.lte = new Date(endDate);
    }
  }
  if (jobId) where.jobId = jobId;

  if (hasAnyRole(user.role, [UserRole.Admin])) {
    if (companyId) where.job = { companyId };
  } else {
    if (user.companyId) {
      where.job = { companyId: user.companyId };
    }
  }

  const [total, shifts] = await Promise.all([
    prisma.shift.count({ where }),
    prisma.shift.findMany({
      where,
      include: {
        job: true,
        assignedPersonnel: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                avatarUrl: true,
              },
            },
          },
        },
      },
      orderBy: { date: 'desc' },
      skip: (page - 1) * pageSize,
      take: pageSize,
    }),
  ]);

  return {
    shifts,
    total,
    pages: Math.ceil(total / pageSize),
  };
}

export async function getShiftById(
  user: User,
  id: string
): Promise<ShiftWithDetails | null> {
  const shift = await prisma.shift.findUnique({
    where: { id },
    include: {
      job: true,
      assignedPersonnel: {
        include: {
          user: {
            select: {
              id: true,
              name: true,
              avatarUrl: true,
            },
          },
        },
      },
    },
  });

  if (!shift) return null;

  if (
    !hasAnyRole(user.role, [UserRole.Admin]) &&
    user.companyId !== shift.job.companyId
  ) {
    throw new Error('Not authorized to view this shift');
  }

  return shift;
}

export async function createShift(
  user: User,
  data: {
    jobId: string;
    date: Date;
    startTime: Date;
    endTime: Date;
    requestedWorkers: number;
    crewChiefId?: string;
  }
): Promise<Shift> {
  const job = await prisma.job.findUnique({ where: { id: data.jobId } });
  if (!job) throw new Error('Job not found');

  if (
    !hasAnyRole(user.role, [UserRole.Admin]) &&
    user.companyId !== job.companyId
  ) {
    throw new Error('Not authorized to create a shift for this job');
  }

  return prisma.shift.create({
    data: {
      jobId: data.jobId,
      date: data.date,
      startTime: data.startTime,
      endTime: data.endTime,
      requestedWorkers: data.requestedWorkers,
      status: 'Pending',
      assignedPersonnel: data.crewChiefId
        ? {
            create: {
              userId: data.crewChiefId,
              roleCode: 'CC',
            },
          }
        : undefined,
    },
  });
}

export async function updateShift(
  user: User,
  id: string,
  data: Partial<Shift>
): Promise<Shift> {
  const shift = await prisma.shift.findUnique({
    where: { id },
    include: { job: true },
  });
  if (!shift) throw new Error('Shift not found');

  if (
    !hasAnyRole(user.role, [UserRole.Admin]) &&
    user.companyId !== shift.job.companyId
  ) {
    throw new Error('Not authorized to update this shift');
  }

  return prisma.shift.update({
    where: { id },
    data,
  });
}

export async function deleteShift(user: User, id: string): Promise<void> {
  const shift = await prisma.shift.findUnique({
    where: { id },
    include: { job: true },
  });
  if (!shift) throw new Error('Shift not found');

  if (
    !hasAnyRole(user.role, [UserRole.Admin]) &&
    user.companyId !== shift.job.companyId
  ) {
    throw new Error('Not authorized to delete this shift');
  }

  await prisma.shift.delete({
    where: { id },
  });
}

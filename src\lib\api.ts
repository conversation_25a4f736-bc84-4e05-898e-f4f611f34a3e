interface ApiError extends Error {
  status?: number
  details?: unknown
}

export interface Timesheet {
  id: string
  status: 'draft' | 'pending_approval' | 'approved' | 'rejected'
  clientId?: string
  managerId?: string
}

export interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: {
    message: string
    code?: string
  }
}

export const api = {
  async get<T>(path: string): Promise<T> {
    const res = await fetch(`/api${path}`)
    if (!res.ok) {
      const error: ApiError = new Error(await res.text())
      error.status = res.status
      throw error
    }

    const response: ApiResponse<T> = await res.json()
    if (!response.success) {
      throw new Error(response.error?.message || 'Unknown API error')
    }

    return response.data!
  },
  
  async post<T>(path: string, body: unknown): Promise<T> {
    const res = await fetch(`/api${path}`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(body)
    })

    if (!res.ok) {
      const error: ApiError = new Error(await res.text())
      error.status = res.status
      error.details = await res.json().catch(() => undefined)
      throw error
    }

    const response: ApiResponse<T> = await res.json()
    if (!response.success) {
      throw new Error(response.error?.message || 'Unknown API error')
    }

    return response.data!
  }
}

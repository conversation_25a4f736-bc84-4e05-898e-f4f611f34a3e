import type { Company, Job, Shift, User, UserRole } from '@prisma/client';

export const mockUsers: Record<UserRole, User> = {
  Staff: { id: 'staff1', name: 'Staff User', email: '<EMAIL>', role: 'Staff', passwordHash: 'hashedpassword', isActive: true, createdAt: new Date(), updatedAt: new Date(), lastLogin: null, certifications: [], performance: null, location: null, companyId: null, avatarUrl: null },
  Admin: { id: 'adm1', name: 'Admin User', email: '<EMAIL>', role: 'Admin', passwordHash: 'hashedpassword', isActive: true, createdAt: new Date(), updatedAt: new Date(), lastLogin: null, certifications: [], performance: null, location: null, companyId: null, avatarUrl: null },
  CompanyUser: { id: 'cli-user1', name: '<PERSON>', email: '<EMAIL>', role: 'CompanyUser', companyId: 'cli1', passwordHash: 'hashedpassword', isActive: true, createdAt: new Date(), updatedAt: new Date(), lastLogin: null, certifications: [], performance: null, location: null, avatarUrl: null },
  CrewChief: { id: 'cc1', name: 'Maria Garcia', email: '<EMAIL>', role: 'CrewChief', passwordHash: 'hashedpassword', isActive: true, createdAt: new Date(), updatedAt: new Date(), lastLogin: null, certifications: [], performance: null, location: null, companyId: null, avatarUrl: null },
  Employee: { id: 'emp2', name: 'Maria Garcia', email: '<EMAIL>', role: 'Employee', passwordHash: 'hashedpassword', isActive: true, createdAt: new Date(), updatedAt: new Date(), lastLogin: null, certifications: [], performance: null, location: null, companyId: null, avatarUrl: null },
};

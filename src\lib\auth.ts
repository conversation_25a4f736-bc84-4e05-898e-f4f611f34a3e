import { prisma } from '@/lib/prisma';
import { User, UserRole } from '@prisma/client';
import bcrypt from 'bcryptjs';

type AuthenticatedUser = {
  id: string;
  role: UserRole;
} | null;

export function hasRequiredRole(user: AuthenticatedUser, requiredRoles: UserRole[]): boolean {
  if (!user) {
    return false;
  }
  return requiredRoles.includes(user.role);
}

export function hasAdminAccess(user: AuthenticatedUser): boolean {
  return user?.role === UserRole.Admin;
}

export function hasAnyRole(user: AuthenticatedUser, roles: UserRole[]): boolean {
  if (!user) {
    return false;
  }
  return roles.includes(user.role);
}

export async function hashPassword(password: string): Promise<string> {
  const salt = await bcrypt.genSalt(10);
  return bcrypt.hash(password, salt);
}

export async function getUserByEmail(email: string) {
  return prisma.user.findUnique({ where: { email } });
}

export async function createUser(data: any) {
  const { password, ...rest } = data;
  const passwordHash = await hashPassword(password);
  return prisma.user.create({
    data: { ...rest, passwordHash },
  });
}

export async function authenticateUser(email: string, password: string): Promise<User | null> {
  const user = await getUserByEmail(email);
  if (!user || !user.passwordHash) {
    return null;
  }
  const isValid = await bcrypt.compare(password, user.passwordHash);
  return isValid ? user : null;
}

export async function refreshUserData(userId: string) {
    return prisma.user.findUnique({ where: { id: userId } });
}

export function verifySignatureRequest(signature: string, data: any): boolean {
    // This is a placeholder. In a real application, you would use a library like
    // ethers.js to verify the signature.
    return true;
}

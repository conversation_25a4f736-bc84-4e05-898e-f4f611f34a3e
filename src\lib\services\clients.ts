import { prisma } from '../prisma';
import { Prisma, Company, UserRole } from '@prisma/client';
import bcrypt from 'bcryptjs';
import crypto from 'crypto';
import { User } from 'next-auth';
import { hasAnyRole } from '../auth';

export async function getAllCompanies(user: User): Promise<Company[]> {
  if (!hasAnyRole(user.role, [UserRole.Admin])) {
    throw new Error('Not authorized to view all companies');
  }
  return prisma.company.findMany({
    orderBy: {
      name: 'asc',
    },
    include: {
      _count: {
        select: { users: true, jobs: true },
      },
    },
  });
}

export async function getClientById(id: string): Promise<Company | null> {
  return prisma.company.findUnique({
    where: { id },
    include: {
      users: {
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
        },
      },
      jobs: {
        select: {
          id: true,
          name: true,
          status: true,
          startDate: true,
        },
        orderBy: {
          startDate: 'desc',
        },
      },
    },
  });
}

export async function getCompanyById(
  user: User,
  id: string
): Promise<Company | null> {
  if (
    !hasAnyRole(user.role, [UserRole.Admin]) &&
    user.companyId !== id
  ) {
    throw new Error('Not authorized to view this company');
  }
  return prisma.company.findUnique({
    where: { id },
    include: {
      users: {
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
        },
      },
      jobs: {
        select: {
          id: true,
          name: true,
          status: true,
          startDate: true,
        },
        orderBy: {
          startDate: 'desc',
        },
      },
    },
  });
}

export async function createCompany(
  user: User,
  data: {
    name: string;
    address?: string;
    email?: string;
    phone?: string;
    contact_name: string;
    contact_email: string;
    contact_phone?: string;
  }
): Promise<Company> {
  if (!hasAnyRole(user.role, [UserRole.Admin])) {
    throw new Error('Not authorized to create a company');
  }
  return prisma.$transaction(async (tx: Prisma.TransactionClient) => {
    const company = await tx.company.create({
      data: {
        name: data.name,
        address: data.address,
        email: data.email,
        phone: data.phone,
      },
    });

    // Generate a secure, random password for the new company user
    const temporaryPassword = crypto.randomBytes(12).toString('hex');
    const hashedPassword = await bcrypt.hash(temporaryPassword, 12);

    await tx.user.create({
      data: {
        name: data.contact_name,
        email: data.contact_email,
        passwordHash: hashedPassword,
        role: UserRole.CompanyUser,
        companyId: company.id,
      },
    });

    return company;
  });
}

export async function updateClient(id: string, data: Partial<Company>): Promise<Company> {
  return prisma.company.update({
    where: { id },
    data,
  });
}

export async function updateCompany(
  user: User,
  id: string,
  data: Partial<Company>
): Promise<Company> {
  if (!hasAnyRole(user.role, [UserRole.Admin])) {
    throw new Error('Not authorized to update a company');
  }
  return prisma.company.update({
    where: { id },
    data,
  });
}

export async function deleteClient(id: string): Promise<void> {
  // The cascading delete rule in the schema will handle deleting related users.
  await prisma.company.delete({ where: { id } });
}

export async function deleteCompany(user: User, id: string): Promise<void> {
  if (!hasAnyRole(user.role, [UserRole.Admin])) {
    throw new Error('Not authorized to delete a company');
  }
  // The cascading delete rule in the schema will handle deleting related users.
  await prisma.company.delete({ where: { id } });
}

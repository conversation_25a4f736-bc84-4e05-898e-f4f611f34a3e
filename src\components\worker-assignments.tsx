"use client"

import React from "react"
import { <PERSON><PERSON> } from '@/components/ui/button'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from '@/components/ui/badge'

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { UserPlus, X } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { AssignedPersonnel, RoleCode } from "@/lib/types";

interface WorkerAssignmentsProps {
  shiftId: string;
  assignedPersonnel: AssignedPersonnel[];
  workerRequirements: { roleCode: RoleCode; requiredCount: number }[];
  availableEmployees: any[];
  onUpdate: () => void;
}

const ROLE_DEFINITIONS: Record<RoleCode, { name: string; color: string; bgColor: string; borderColor: string }> = {
  'CC': { name: 'Crew Chief', color: 'text-purple-700', bgColor: 'bg-purple-50', borderColor: 'border-purple-200' },
  'SH': { name: 'Stage Hand', color: 'text-blue-700', bgColor: 'bg-blue-50', borderColor: 'border-blue-200' },
  'FO': { name: 'Fork Operator', color: 'text-green-700', bgColor: 'bg-green-50', borderColor: 'border-green-200' },
  'RFO': { name: 'Reach Fork Operator', color: 'text-yellow-700', bgColor: 'bg-yellow-50', borderColor: 'border-yellow-200' },
  'RG': { name: 'Rigger', color: 'text-red-700', bgColor: 'bg-red-50', borderColor: 'border-red-200' },
  'GL': { name: 'General Labor', color: 'text-gray-700', bgColor: 'bg-gray-50', borderColor: 'border-gray-200' },
} as const

export default function WorkerAssignments({
  shiftId,
  assignedPersonnel,
  workerRequirements,
  availableEmployees,
  onUpdate
}: WorkerAssignmentsProps) {
  const { toast } = useToast()

  const getEligibleEmployees = (roleCode: RoleCode) => {
    return availableEmployees.filter(employee => {
      if (employee.role === 'Admin') {
        return true
      }
      switch (roleCode) {
        case 'CC':
          return employee.crewChiefEligible || employee.role === 'Crew Chief'
        case 'FO':
        case 'RFO':
          return employee.forkOperatorEligible
        default:
          return true
      }
    })
  }

  const checkTimeConflicts = async (employeeId: string) => {
    try {
      const response = await fetch(`/api/shifts/${shiftId}/check-conflicts`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ employeeId })
      })
      if (!response.ok) {
        return { hasConflicts: false, conflicts: [] }
      }
      const data = await response.json()
      return data
    } catch (error) {
      console.error('Error checking conflicts:', error)
      return { hasConflicts: false, conflicts: [] }
    }
  }

  const assignWorker = async (employeeId: string, roleCode: RoleCode) => {
    try {
      const employee = availableEmployees.find(emp => emp.id === employeeId)
      if (!employee) return

      const conflictCheck = await checkTimeConflicts(employeeId)
      if (conflictCheck.hasConflicts && conflictCheck.conflicts.length > 0) {
        const conflict = conflictCheck.conflicts[0]
        toast({
          title: "Time Conflict",
          description: `${employee.name} is already assigned to ${conflict.clientName} - ${conflict.jobName} from ${conflict.startTime} to ${conflict.endTime} on the same day`,
          variant: "destructive",
        })
        return
      }

      const response = await fetch(`/api/shifts/${shiftId}/assign-worker`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          employeeId,
          roleCode,
          roleOnShift: ROLE_DEFINITIONS[roleCode].name
        })
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to assign worker')
      }

      toast({
        title: "Worker Assigned",
        description: `${employee.name} assigned as ${ROLE_DEFINITIONS[roleCode].name}`,
      })
      onUpdate()
    } catch (error) {
      console.error('Error assigning worker:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to assign worker",
        variant: "destructive",
      })
    }
  }

  const unassignWorker = async (assignmentId: string, workerName: string) => {
    try {
      const response = await fetch(`/api/shifts/${shiftId}/assigned/${assignmentId}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to unassign worker')
      }

      toast({
        title: "Worker Unassigned",
        description: `${workerName} has been unassigned from this shift`,
      })
      onUpdate()
    } catch (error) {
      console.error('Error unassigning worker:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to unassign worker",
        variant: "destructive",
      })
    }
  }

  const getRequiredCount = (roleCode: RoleCode): number => {
    return workerRequirements.find(req => req.roleCode === roleCode)?.requiredCount || 0
  }

  const getAssignedWorkers = (roleCode: RoleCode): AssignedPersonnel[] => {
    return assignedPersonnel.filter(worker => worker.roleCode === roleCode)
  }

  const generateWorkerSlots = (roleCode: RoleCode): { type: string, worker?: AssignedPersonnel, roleCode: RoleCode }[] => {
    const requiredCount = getRequiredCount(roleCode)
    const assignedWorkers = getAssignedWorkers(roleCode)
    const slots: { type: string, worker?: AssignedPersonnel, roleCode: RoleCode }[] = []

    assignedWorkers.forEach(worker => {
      slots.push({ type: 'assigned', worker, roleCode })
    })

    const emptySlots = Math.max(0, requiredCount - assignedWorkers.length)
    for (let i = 0; i < emptySlots; i++) {
      slots.push({ type: 'empty', roleCode })
    }

    return slots
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <UserPlus className="h-5 w-5" />
          Worker Assignments
        </CardTitle>
        <CardDescription>
          Assign specific workers to each required position
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {(Object.entries(ROLE_DEFINITIONS) as [RoleCode, typeof ROLE_DEFINITIONS[RoleCode]][]).map(([roleCode, roleDef]) => {
            const slots = generateWorkerSlots(roleCode)
            
            if (slots.length === 0) return null

            return (
              <div key={roleCode} className="space-y-2">
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className={`${roleDef.color} ${roleDef.bgColor}`}>
                    {roleCode}
                  </Badge>
                  <span className={`font-medium ${roleDef.color}`}>{roleDef.name}</span>
                  <span className="text-sm text-muted-foreground">
                    ({getAssignedWorkers(roleCode).length}/{getRequiredCount(roleCode)} assigned)
                  </span>
                </div>
                
                <div className="grid gap-2 md:grid-cols-2 lg:grid-cols-3">
                  {slots.map((slot, index) => (
                    <div 
                      key={`${roleCode}-${index}`} 
                      className={`p-3 rounded-lg border ${roleDef.bgColor} ${roleDef.borderColor}`}
                    >
                      {slot.type === 'assigned' ? (
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Avatar className="h-8 w-8">
                              <AvatarImage src={slot.worker.employee.avatar} />
                              <AvatarFallback>
                                {slot.worker.employee.name.split(' ').map((n: string) => n[0]).join('')}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <div className="font-medium text-sm">{slot.worker.employee.name}</div>
                              <div className={`text-xs ${roleDef.color}`}>{slot.worker.roleOnShift}</div>
                            </div>
                          </div>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => unassignWorker(slot.worker.id, slot.worker.employee.name)}
                            className="h-6 w-6 p-0 text-muted-foreground hover:text-destructive"
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </div>
                      ) : (
                        <Select onValueChange={(employeeId) => assignWorker(employeeId, roleCode)}>
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Select worker..." />
                          </SelectTrigger>
                          <SelectContent>
                            {getEligibleEmployees(roleCode)
                              .filter(emp => !assignedPersonnel.some(assigned => assigned.employee.id === emp.id))
                              .map(employee => (
                                <SelectItem key={employee.id} value={employee.id}>
                                  <div className="flex items-center justify-between w-full">
                                    <span>{employee.name}</span>
                                    <div className="flex gap-1">
                                      {employee.role === 'Admin' && (
                                        <Badge variant="secondary" className="text-xs">Manager</Badge>
                                      )}
                                      {roleCode === 'CC' && employee.crewChiefEligible && (
                                        <span className="text-xs text-muted-foreground">(CC Eligible)</span>
                                      )}
                                      {(roleCode === 'FO' || roleCode === 'RFO') && employee.forkOperatorEligible && (
                                        <span className="text-xs text-muted-foreground">(FO Eligible)</span>
                                      )}
                                    </div>
                                  </div>
                                </SelectItem>
                              ))}
                            {getEligibleEmployees(roleCode)
                              .filter(emp => !assignedPersonnel.some(assigned => assigned.employee.id === emp.id))
                              .length === 0 && (
                              <SelectItem value="no-workers" disabled>
                                No eligible workers available
                              </SelectItem>
                            )}
                          </SelectContent>
                        </Select>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}
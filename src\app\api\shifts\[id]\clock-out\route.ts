import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getCurrentUser } from '@/lib/middleware';
import { UserRole } from '@prisma/client';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    if (![UserRole.Admin, UserRole.CrewChief].includes(user.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    const { id: shiftId } = await params;
    const body = await request.json();
    const { workerId } = body;

    if (!workerId) {
      return NextResponse.json(
        { error: 'Worker ID is required' },
        { status: 400 }
      );
    }

    // Find the active time entry
    const activeEntryResult = await prisma.timeEntry.findFirst({
      where: {
        assignedPersonnelId: workerId,
        isActive: true,
      },
      select: { id: true, entryNumber: true },
    });

    if (!activeEntryResult) {
      return NextResponse.json(
        { error: 'No active clock-in found for this employee' },
        { status: 400 }
      );
    }

    const activeEntry = activeEntryResult;
    const now = new Date().toISOString();

    // Update the time entry with clock out time
    await prisma.timeEntry.update({
      where: { id: activeEntry.id },
      data: {
        clockOut: now,
        isActive: false,
      },
    });

      return NextResponse.json({
        success: true,
        message: 'Employee clocked out successfully',
      });
    } catch (error) {
      console.error('Error clocking out employee:', error);
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      );
    }
}

'use client';

import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Shield, ShieldCheck, ShieldX, Crown, Building, Briefcase } from 'lucide-react';
import { useCrewChiefPermissions, getPermissionDescription, getPermissionLevel } from '@/hooks/useCrewChiefPermissions';
import type { CrewChiefPermissionCheck } from '@/lib/types';

interface CrewChiefPermissionBadgeProps {
  shiftId: string;
  showTooltip?: boolean;
}

export function CrewChiefPermissionBadge({
  shiftId,
  showTooltip = true,
}: CrewChiefPermissionBadgeProps) {
  const { hasPermission, permissionCheck, isLoading } = useCrewChiefPermissions(shiftId);

  if (isLoading) {
    return (
      <Badge variant="outline">
        <Shield className="mr-1 h-4 w-4" />
        Checking...
      </Badge>
    );
  }

  const permissionLevel = getPermissionLevel(permissionCheck);
  const description = getPermissionDescription(permissionCheck);

  const getBadgeVariant = () => {
    if (!hasPermission) return 'destructive';

    switch (permissionLevel) {
      case 'designated':
        return 'default';
      case 'client':
        return 'secondary';
      case 'job':
        return 'outline';
      case 'shift':
        return 'default';
      default:
        return 'secondary';
    }
  };

  const getIcon = () => {
    if (!hasPermission) return <ShieldX className="mr-1 h-4 w-4" />;

    switch (permissionLevel) {
      case 'designated':
        return <Crown className="mr-1 h-4 w-4" />;
      case 'client':
        return <Building className="mr-1 h-4 w-4" />;
      case 'job':
        return <Briefcase className="mr-1 h-4 w-4" />;
      case 'shift':
        return <ShieldCheck className="mr-1 h-4 w-4" />;
      default:
        return <Shield className="mr-1 h-4 w-4" />;
    }
  };

  const getShortText = () => {
    if (!hasPermission) return 'No Access';

    switch (permissionLevel) {
      case 'designated':
        return 'Crew Chief';
      case 'client':
        return 'Client Access';
      case 'job':
        return 'Job Access';
      case 'shift':
        return 'Shift Access';
      default:
        return 'Access';
    }
  };

  const badge = (
    <Badge variant={getBadgeVariant()}>
      {getIcon()}
      {getShortText()}
    </Badge>
  );

  if (!showTooltip) {
    return badge;
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>{badge}</TooltipTrigger>
        <TooltipContent>
          <p>{description}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

interface PermissionGuardProps {
  shiftId: string;
  children: React.ReactNode;
  fallback?: React.ReactNode;
  requirePermission?: boolean;
}

export function PermissionGuard({
  shiftId,
  children,
  fallback = null,
  requirePermission = true
}: PermissionGuardProps) {
  const { hasPermission, isLoading } = useCrewChiefPermissions(shiftId);

  if (isLoading) {
    return <div>Loading permissions...</div>;
  }

  if (requirePermission && !hasPermission) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

interface PermissionStatusProps {
  permissionCheck: CrewChiefPermissionCheck | null;
  isLoading?: boolean;
}

export function PermissionStatus({ permissionCheck, isLoading }: PermissionStatusProps) {
  if (isLoading) {
    return (
      <div className="flex items-center gap-2">
        <Shield className="h-4 w-4" />
        <span>Checking permissions...</span>
      </div>
    );
  }

  if (!permissionCheck || !permissionCheck.hasPermission) {
    return (
      <div className="flex items-center gap-2 text-red-600">
        <ShieldX className="h-4 w-4" />
        <span>No crew chief permissions</span>
      </div>
    );
  }

  const description = getPermissionDescription(permissionCheck);
  const level = getPermissionLevel(permissionCheck);

  return (
    <div className="flex items-center gap-2 text-green-600">
      {level === 'designated' && <Crown className="h-4 w-4" />}
      {level === 'client' && <Building className="h-4 w-4" />}
      {level === 'job' && <Briefcase className="h-4 w-4" />}
      {level === 'shift' && <ShieldCheck className="h-4 w-4" />}
      {level === 'none' && <Shield className="h-4 w-4" />}
      <span>{description}</span>
    </div>
  );
}

'use client';

import { useUser } from '@/hooks/use-user';
import { useApi } from '@/hooks/use-api';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useEffect, useState } from 'react';
import { ChevronDown, ChevronUp } from 'lucide-react';
import ShiftManager from '@/components/dashboard/shift-management/shift-manager';
import { ShiftWithDetails } from '@/components/dashboard/shift-management/types';
import { Shift, Job, Company } from '@prisma/client';

type ActiveShift = Shift & {
  job: Job & {
    company: Company;
  };
};

type DashboardData = {
  activeShifts: ActiveShift[];
};

export default function CrewChiefDashboard() {
  const { user } = useUser();
  const [expandedShift, setExpandedShift] = useState<string | null>(null);

  const { data, loading, error, refetch } = useApi<DashboardData>(
    user?.id ? `/api/crew-chief/${user.id}/dashboard` : null
  );
  const { data: shiftDetails, loading: shiftDetailsLoading, error: shiftDetailsError, refetch: refetchShiftDetails } = useApi<ShiftWithDetails>(
    expandedShift ? `/api/shifts/${expandedShift}/details` : null
  );

  useEffect(() => {
    refetch();
  }, [refetch]);

  const handleToggleShift = (shiftId: string) => {
    if (expandedShift === shiftId) {
      setExpandedShift(null);
    } else {
      setExpandedShift(shiftId);
    }
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div className="text-red-500">Error loading dashboard data.</div>;
  }

  const activeShifts = data?.activeShifts || [];

  return (
    <div className="space-y-6">
      <header>
        <h1 className="text-3xl font-bold">Welcome, {user?.name}!</h1>
        <p className="text-muted-foreground">Crew Chief Dashboard</p>
      </header>

      <section>
        <h2 className="text-2xl font-semibold mb-4">My Active Shifts</h2>
        {activeShifts.length > 0 ? (
          <div className="space-y-4">
            {activeShifts.map((shift) => (
              <Card key={shift.id}>
                <CardHeader>
                  <div className="flex justify-between items-center">
                    <div>
                      <CardTitle>{shift.job.name}</CardTitle>
                      <CardDescription>{shift.job.company.name} - {new Date(shift.date).toLocaleDateString()}</CardDescription>
                    </div>
                    <div className="flex items-center gap-4">
                      <Badge>{shift.status}</Badge>
                      <Button variant="outline" size="sm" onClick={() => handleToggleShift(shift.id)}>
                        {expandedShift === shift.id ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                {expandedShift === shift.id && (
                  <CardContent>
                    {shiftDetailsLoading && <p>Loading shift details...</p>}
                    {shiftDetailsError && <p className="text-red-500">Error loading details.</p>}
                    {shiftDetails && (
                      <ShiftManager
                        shift={shiftDetails}
                        onUpdate={() => refetchShiftDetails()}
                      />
                    )}
                  </CardContent>
                )}
              </Card>
            ))}
          </div>
        ) : (
          <p>No active shifts found.</p>
        )}
      </section>
    </div>
  );
}

'use client';

import { useUser } from '@/hooks/use-user';
import { useApi } from '@/hooks/use-api';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { ArrowRight, Calendar } from 'lucide-react';
import { Shift, Job, Company } from '@prisma/client';

type UpcomingShift = Shift & {
  job: Job & {
    company: Company;
  };
};

type DashboardData = {
  upcomingShifts: UpcomingShift[];
};

export default function EmployeeDashboard() {
  const { user } = useUser();
  const { data, loading, error } = useApi<DashboardData>(
    user?.id ? `/api/employees/${user.id}/dashboard` : null
  );

  if (loading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div className="text-red-500">Error loading dashboard data.</div>;
  }

  return (
    <div className="space-y-6">
      <header>
        <h1 className="text-3xl font-bold">Welcome, {user?.name}!</h1>
      </header>

      <Card>
        <CardHeader>
          <CardTitle>My Upcoming Shifts</CardTitle>
        </CardHeader>
        <CardContent>
          {data?.upcomingShifts && data.upcomingShifts.length > 0 ? (
            <div className="space-y-4">
              {data.upcomingShifts.map((shift) => (
                <div key={shift.id} className="flex items-center justify-between">
                  <div>
                    <p className="font-semibold">{shift.job.name}</p>
                    <p className="text-sm text-muted-foreground">
                      {shift.job.company.name} - {new Date(shift.date).toLocaleDateString()}
                    </p>
                  </div>
                  <Badge>{shift.status}</Badge>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-center text-muted-foreground py-8">No upcoming shifts</p>
          )}
          <Button asChild className="mt-4">
            <Link href="/shifts">
              View All My Shifts <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}

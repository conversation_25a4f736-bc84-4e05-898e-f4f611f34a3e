'use client';

import { useUser } from '@/hooks/use-user';
import { useApi } from '@/hooks/use-api';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { ArrowRight, Calendar } from 'lucide-react';
import { Shift, Job, Company } from '@prisma/client';

type UpcomingShift = Shift & {
  job: Job & {
    company: Company;
  };
};

type DashboardData = {
  upcomingShifts: UpcomingShift[];
};

export default function EmployeeDashboard() {
  const { user } = useUser();
  const { data, loading, error } = useApi<DashboardData>(
    user?.id ? `/api/employees/${user.id}/dashboard` : null
  );

  if (loading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div className="text-red-500">Error loading dashboard data.</div>;
  }

  return (
    <div className="mobile-container space-y-4 pb-20"> {/* Extra bottom padding for mobile nav */}
      <header className="text-center mb-6">
        <h1 className="text-2xl md:text-3xl font-bold mobile-text-xl">
          Welcome, {user?.name?.split(' ')[0]}! 👋
        </h1>
        <p className="text-muted-foreground mt-2">
          {new Date().toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          })}
        </p>
      </header>

      {/* Quick Actions */}
      <div className="grid grid-cols-2 gap-3 mb-6">
        <Button asChild className="mobile-button h-20 flex-col">
          <Link href="/shifts">
            <Calendar className="h-6 w-6 mb-1" />
            <span className="text-sm font-medium">My Shifts</span>
          </Link>
        </Button>
        <Button asChild variant="outline" className="mobile-button h-20 flex-col">
          <Link href="/timesheet">
            <ArrowRight className="h-6 w-6 mb-1" />
            <span className="text-sm font-medium">Clock In/Out</span>
          </Link>
        </Button>
      </div>

      <Card className="mobile-card">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 mobile-text-lg">
            <Calendar className="h-5 w-5 text-primary" />
            Upcoming Shifts
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          {data?.upcomingShifts && data.upcomingShifts.length > 0 ? (
            <div className="space-y-3">
              {data.upcomingShifts.slice(0, 3).map((shift) => (
                <div key={shift.id} className="mobile-list-item bg-gray-50 rounded-lg border-0 p-4">
                  <div className="flex-1">
                    <p className="font-semibold mobile-text-base text-gray-900">
                      {shift.job.name}
                    </p>
                    <p className="text-sm text-gray-600 mt-1">
                      {shift.job.company.name}
                    </p>
                    <p className="text-sm text-gray-500 mt-1">
                      📅 {new Date(shift.date).toLocaleDateString('en-US', {
                        weekday: 'short',
                        month: 'short',
                        day: 'numeric'
                      })}
                    </p>
                  </div>
                  <div className="flex flex-col items-end gap-2">
                    <Badge variant={shift.status === 'Confirmed' ? 'default' : 'secondary'}>
                      {shift.status}
                    </Badge>
                  </div>
                </div>
              ))}
              {data.upcomingShifts.length > 3 && (
                <div className="text-center pt-2">
                  <Button asChild variant="ghost" size="sm">
                    <Link href="/shifts">
                      View {data.upcomingShifts.length - 3} more shifts
                    </Link>
                  </Button>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-8">
              <Calendar className="h-12 w-12 text-gray-300 mx-auto mb-3" />
              <p className="text-gray-500 mobile-text-base">No upcoming shifts</p>
              <p className="text-sm text-gray-400 mt-1">Check back later for new assignments</p>
            </div>
          )}
          <Button asChild className="mobile-button mt-4">
            <Link href="/shifts">
              View All My Shifts <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}

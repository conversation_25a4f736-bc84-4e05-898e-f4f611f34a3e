import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from '@/lib/middleware'
import { prisma } from '@/lib/prisma'
import { UserRole } from '@prisma/client'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Only crew chiefs and admins can finalize timesheets
    if (![UserRole.CrewChief, UserRole.Admin].includes(user.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions. Only Crew Chiefs and <PERSON><PERSON> can finalize timesheets.' },
        { status: 403 }
      );
    }

    const { id: shiftId } = await params;

    console.log(`SIMPLE Finalize timesheet request (UPDATED):`, { shiftId, userId: user.id, userRole: user.role });

    // Check if all workers have ended their shifts
    const activeWorkersResult = await prisma.timeEntry.count({
      where: {
        assignedPersonnel: {
          shiftId,
        },
        isActive: true,
      },
    });

    const activeCount = activeWorkersResult;

    if (activeCount > 0) {
      return NextResponse.json(
        { error: `Cannot finalize timesheet. ${activeCount} workers have not ended their shifts yet.` },
        { status: 400 }
      );
    }

    // Check if timesheet already exists
    const existingTimesheetResult = await prisma.timesheet.findUnique({
      where: { shiftId },
      select: { id: true },
    });

    let timesheetId;

    if (existingTimesheetResult) {
      timesheetId = existingTimesheetResult.id;
      await prisma.timesheet.update({
        where: { id: timesheetId },
        data: {
          status: 'Pending',
          // submitted_by: user.id, // This field is not in the schema
          // submitted_at: new Date(),
        },
      });
    } else {
      const newTimesheetResult = await prisma.timesheet.create({
        data: {
          shiftId,
          status: 'Pending',
          // submitted_by: user.id,
          // submitted_at: new Date(),
        },
        select: { id: true },
      });
      timesheetId = newTimesheetResult.id;
    }

    // Update shift status to "Pending Customer Approval" as per requirements
    console.log(`SIMPLE Updating shift status to 'Pending Client Approval' for shift:`, shiftId);
    await prisma.shift.update({
      where: { id: shiftId },
      data: { status: 'Pending' },
    });

    // Notifications are not implemented in the schema

    console.log(`SIMPLE Timesheet finalized successfully:`, { timesheetId, shiftId });

    return NextResponse.json({
      success: true,
      message: 'Timesheet finalized and submitted for client approval',
      timesheetId
    });

  } catch (error) {
    console.error('SIMPLE Error finalizing timesheet:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

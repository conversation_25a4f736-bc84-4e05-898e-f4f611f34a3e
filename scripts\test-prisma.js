// Simple Prisma connection test
const { PrismaClient } = require('@prisma/client')
const prisma = new PrismaClient()

async function test() {
  try {
    const users = await prisma.user.findMany({ take: 1 })
    console.log('Connected successfully. Found users:', users.length)
    console.log('First user:', users[0]?.email || 'No users found')
  } catch (err) {
    console.error('Connection error:', err)
  } finally {
    await prisma.$disconnect()
  }
}

test()

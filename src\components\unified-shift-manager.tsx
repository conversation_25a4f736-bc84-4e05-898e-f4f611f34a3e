"use client"

import React, { useState, useEffect } from "react"
import { AssignedPersonnel, RoleCode } from "@/lib/types";
import { useApi } from "@/hooks/use-api"
import WorkerRequirements from "./worker-requirements"
import WorkerAssignments from "./worker-assignments"
import TimesheetManagement from "./timesheet-management"

interface WorkerRequirement {
  roleCode: RoleCode;
  requiredCount: number;
}

interface UnifiedShiftManagerProps {
  shiftId: string;
  initialAssignedPersonnel: AssignedPersonnel[];
  onUpdate: () => void;
}

export default function UnifiedShiftManager({
  shiftId,
  initialAssignedPersonnel: assignedPersonnel,
  onUpdate
}: UnifiedShiftManagerProps) {
  const [workerRequirements, setWorkerRequirements] = useState<WorkerRequirement[]>([])

  // Fetch worker requirements
  const { data: requirementsData, loading: requirementsLoading } = useApi<{ workerRequirements: WorkerRequirement[] }>(
    `/api/shifts/${shiftId}/worker-requirements`
  )

  // Fetch available employees for assignment
  const { data: usersData } = useApi<{ users: any[] }>('/api/users?role=Employee')
  const availableEmployees = usersData?.users || []

  useEffect(() => {
    if (requirementsData?.workerRequirements) {
      setWorkerRequirements(requirementsData.workerRequirements)
    } else if (requirementsData && requirementsData.workerRequirements?.length === 0) {
      // Initialize with default requirements if none exist
      const defaultRequirements: WorkerRequirement[] = [
        { roleCode: 'CC', requiredCount: 1 },
        { roleCode: 'SH', requiredCount: 0 },
        { roleCode: 'FO', requiredCount: 0 },
        { roleCode: 'RFO', requiredCount: 0 },
        { roleCode: 'RG', requiredCount: 0 },
        { roleCode: 'GL', requiredCount: 0 },
      ]
      setWorkerRequirements(defaultRequirements)
    }
  }, [requirementsData])

  if (requirementsLoading) {
    return <div>Loading worker requirements...</div>
  }

  return (
    <div className="space-y-6">
      <WorkerRequirements
        shiftId={shiftId}
        workerRequirements={workerRequirements}
        onUpdate={setWorkerRequirements}
      />

      <WorkerAssignments
        shiftId={shiftId}
        assignedPersonnel={assignedPersonnel}
        workerRequirements={workerRequirements}
        availableEmployees={availableEmployees}
        onUpdate={onUpdate}
      />

      {assignedPersonnel.length > 0 && (
        <TimesheetManagement
          shiftId={shiftId}
          assignedPersonnel={assignedPersonnel}
          onUpdate={onUpdate}
        />
      )}
    </div>
  )
}

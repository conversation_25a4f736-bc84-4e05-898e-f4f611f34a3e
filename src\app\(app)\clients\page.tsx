"use client"

import React from "react"
import { useRouter } from "next/navigation"
import { format } from "date-fns"
import Link from "next/link"
import { useUser } from "@/hooks/use-user"
import { useClients } from "@/hooks/use-api"
import { Button, Card, Text, Group, ActionIcon, Badge, Stack, Title } from '@mantine/core'
import { Plus, ExternalLink, Mail, User, Calendar } from "lucide-react"

function ClientsPage() {
  const { user } = useUser()
  const router = useRouter()
  const canEdit = user?.role === 'Admin'
  const { data: clientsData, loading, error } = useClients()

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-muted-foreground">Loading clients...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-destructive">Error loading clients: {error.toString()}</div>
      </div>
    )
  }

  const clients = clientsData?.clients || []

  return (
    <Stack gap="lg">
      <Group justify="space-between">
        <div>
          <Title order={1}>Clients</Title>
          <Text c="dimmed">
            Manage client companies and view their job history
          </Text>
        </div>
        {canEdit && (
          <Button onClick={() => router.push('/clients/new')} leftSection={<Plus size={16} />}>
            Add Client
          </Button>
        )}
      </Group>

      {clients.length > 0 ? (
        <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
          {clients.map(client => (
            <div 
              key={client.id} 
              className="mobile-card cursor-pointer hover:shadow-lg transition-shadow duration-200 active:scale-98"
              onClick={() => router.push(`/clients/${client.id}`)}
            >
              <div className="flex justify-between items-start mb-4">
                <h3 className="font-semibold mobile-text-lg text-gray-900 truncate">
                  {client.companyName || client.name}
                </h3>
              </div>
              
              <div className="space-y-3">
                {client.contactPerson && (
                  <div className="flex items-center gap-3">
                    <User size={18} className="text-gray-500 flex-shrink-0" />
                    <span className="text-sm text-gray-700 truncate">{client.contactPerson}</span>
                  </div>
                )}
                {client.contactEmail && (
                  <div className="flex items-center gap-3">
                    <Mail size={18} className="text-gray-500 flex-shrink-0" />
                    <span className="text-sm text-gray-700 truncate">{client.contactEmail}</span>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      ) : (
        <Card padding="lg" radius="md" withBorder>
          <Stack align="center" justify="center" style={{ minHeight: '300px' }}>
            <Title order={3}>No clients found</Title>
            <Text c="dimmed">
              Get started by adding your first client.
            </Text>
            {canEdit && (
              <Button
                variant="outline"
                size="sm"
                className="mt-4"
                onClick={() => router.push('/clients/new')}
              >
                <Plus className="mr-2 h-4 w-4" />
                Add Client
              </Button>
            )}
          </Stack>
        </Card>
      )}
    </Stack>
  )
}

export default ClientsPage;


interface Employee {
  id: string
  name: string
  avatar?: string | null
}

interface TimeEntry {
  entryNumber: number
  clockIn: string
  clockOut?: string
}

interface AssignedPersonnel {
  id: string
  employee: Employee
  roleCode: string
  timeEntries: TimeEntry[]
}

export interface Timesheet {
  id: string
  status: 'draft' | 'pending_client_approval' | 'pending_final_approval' | 'completed' | 'rejected'
  shiftId: string
  client_signature?: string | null
  client_approved_at?: Date | string | null
  client_notes?: string | null
  manager_signature?: string | null
  manager_approved_at?: Date | string | null
  manager_notes?: string | null
  rejection_reason?: string | null
  pdf_url?: string | null
  shift: {
    id: string
    date: Date | string
    startTime: string
    endTime: string
    location: string
    jobName: string
    clientName: string
    crewChiefName: string
    assignedPersonnel: AssignedPersonnel[]
    job: {
      id: string
      name: string
      client: {
        id: string
        company_name: string
      }
    }
  }
}

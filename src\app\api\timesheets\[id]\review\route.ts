import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/middleware';
import { prisma } from '@/lib/prisma';
import { TimeEntry, TimesheetStatus, UserRole } from '@prisma/client';

// GET /api/timesheets/[id]/review - Get timesheet details for review
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { id: timesheetId } = params;

    const timesheet = await prisma.timesheet.findUnique({
      where: { id: timesheetId },
      include: {
        shift: {
          include: {
            job: { include: { company: true } },
            assignedPersonnel: {
              include: {
                user: true,
                timeEntries: { orderBy: { entryNumber: 'asc' } },
              },
            },
          },
        },
      },
    });

    if (!timesheet) {
      return NextResponse.json(
        { error: 'Timesheet not found' },
        { status: 404 }
      );
    }

    // Check access permissions
    const hasAccess =
      user.role === UserRole.Admin ||
      user.id === timesheet.shift.assignedPersonnel.find(p => p.roleCode === 'CC')?.userId ||
      (user.role === UserRole.CompanyUser && user.companyId === timesheet.shift.job.companyId);

    if (!hasAccess) {
      return NextResponse.json(
        { error: 'Access denied. You do not have permission to view this timesheet.' },
        { status: 403 }
      );
    }

    const assignedPersonnel = timesheet.shift.assignedPersonnel.map(p => {
      let totalMinutes = 0;
      p.timeEntries.forEach((entry: TimeEntry) => {
        if (entry.clockIn && entry.clockOut) {
          const clockIn = new Date(entry.clockIn);
          const clockOut = new Date(entry.clockOut);
          const diffMs = clockOut.getTime() - clockIn.getTime();
          totalMinutes += Math.floor(diffMs / (1000 * 60));
        }
      });
      return {
        ...p,
        totalHours: (totalMinutes / 60).toFixed(2),
        totalMinutes,
      };
    });

    const grandTotalMinutes = assignedPersonnel.reduce((sum, emp) => sum + (emp.totalMinutes || 0), 0);
    const grandTotalHours = (grandTotalMinutes / 60).toFixed(2);

    return NextResponse.json({
      timesheet,
      assignedPersonnel,
      totals: {
        grandTotalHours,
        grandTotalMinutes,
        employeeCount: assignedPersonnel.length,
      },
      permissions: {
        canApprove: user.role === UserRole.Admin || user.role === UserRole.CompanyUser || user.id === timesheet.shift.assignedPersonnel.find(p => p.roleCode === 'CC')?.userId,
        canFinalApprove: user.role === UserRole.Admin,
        isClientUser: user.role === UserRole.CompanyUser,
        isAdmin: user.role === UserRole.Admin,
        isCrewChief: user.id === timesheet.shift.assignedPersonnel.find(p => p.roleCode === 'CC')?.userId,
      },
    });
  } catch (error) {
    console.error('Error fetching timesheet for review:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/timesheets/[id]/review - Update timesheet status (e.g., reject)
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);
    if (!user || user.role !== UserRole.Admin) {
      return NextResponse.json(
        { error: 'Forbidden: Only managers can update timesheet status.' },
        { status: 403 }
      );
    }

    const { id: timesheetId } = params;
    const { status, rejectionReason } = await request.json();

    if (status !== 'rejected') {
      return NextResponse.json(
        { error: 'Invalid status. Only "rejected" is supported.' },
        { status: 400 }
      );
    }

    if (!rejectionReason) {
      return NextResponse.json(
        { error: 'Rejection reason is required.' },
        { status: 400 }
      );
    }

    const updatedTimesheet = await prisma.timesheet.update({
      where: { id: timesheetId },
      data: {
        status: TimesheetStatus.Rejected,
        rejectionReason,
      },
    });

    // TODO: Add notification logic here to inform the crew chief.

    return NextResponse.json({
      message: 'Timesheet has been rejected successfully.',
      timesheetId: updatedTimesheet.id,
    });
  } catch (error) {
    console.error('Error updating timesheet status:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

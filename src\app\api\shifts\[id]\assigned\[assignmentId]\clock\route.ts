import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; assignmentId: string }> }
) {
  try {
    const { id: shiftId, assignmentId } = await params
    const body = await request.json()
    const { action } = body

    console.log(`Clock ${action} request - FIXED:`, { shiftId, assignmentId, action })

    if (!action || !['clock_in', 'clock_out'].includes(action)) {
      return NextResponse.json(
        { error: 'Invalid action. Must be clock_in or clock_out' },
        { status: 400 }
      )
    }



    if (action === 'clock_in') {
      const activeEntryCheck = await prisma.timeEntry.findFirst({
        where: {
          assignedPersonnelId: assignmentId,
          clockOut: null,
        },
        orderBy: { entryNumber: 'desc' },
      });

      if (activeEntryCheck) {
        return NextResponse.json(
          { error: 'Worker is already clocked in' },
          { status: 400 }
        )
      }

      const lastEntry = await prisma.timeEntry.findFirst({
        where: { assignedPersonnelId: assignmentId },
        orderBy: { entryNumber: 'desc' },
        select: { entryNumber: true },
      });
      const nextEntryNumber = (lastEntry?.entryNumber || 0) + 1;

      const result = await prisma.timeEntry.create({
        data: {
          assignedPersonnelId: assignmentId,
          entryNumber: nextEntryNumber,
          clockIn: new Date(),
        },
        select: { id: true, clockIn: true },
      });

      await prisma.assignedPersonnel.update({
        where: { id: assignmentId },
        data: { status: 'Clocked In' }, // This status is not in the schema
      });

      return NextResponse.json({
        success: true,
        timeEntry: {
          id: result.id,
          entryNumber: nextEntryNumber,
          clockIn: result.clockIn,
          isActive: true,
        },
      });

    } else if (action === 'clock_out') {
      const activeEntry = await prisma.timeEntry.findFirst({
        where: {
          assignedPersonnelId: assignmentId,
          clockOut: null,
        },
        orderBy: { entryNumber: 'desc' },
      });

      if (!activeEntry) {
        return NextResponse.json(
          { error: 'No active time entry found to clock out' },
          { status: 400 }
        )
      }

      const result = await prisma.timeEntry.update({
        where: { id: activeEntry.id },
        data: { clockOut: new Date() },
        select: { clockOut: true },
      });

      await prisma.assignedPersonnel.update({
        where: { id: assignmentId },
        data: { status: 'Clocked Out' }, // This status is not in the schema
      });

      return NextResponse.json({
        success: true,
        timeEntry: {
          id: activeEntry.id,
          entryNumber: activeEntry.entryNumber,
          clockIn: activeEntry.clockIn,
          clockOut: result.clockOut,
          isActive: false,
        },
      });
    }

  } catch (error) {
    console.error('Error processing clock action:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

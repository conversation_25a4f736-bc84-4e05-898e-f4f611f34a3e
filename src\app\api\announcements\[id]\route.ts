import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { announcementValidation } from '@/lib/validation';
import { withValidationAndAuth, pathParamSchemas } from '@/lib/middleware/validation';

const getAnnouncementHandler = async (
  _request: NextRequest,
  context: { user: any; params?: { id: string } }
) => {
  try {
    const { id } = context.params!;

    const announcement = await prisma.announcement.findUnique({
      where: { id },
      include: {
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!announcement) {
      return NextResponse.json(
        { error: 'Announcement not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      announcement,
    });

  } catch (error) {
    console.error('Error fetching announcement:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
};

export const GET = withValidationAndAuth(getAnnouncementHandler, {
  paramsSchema: pathParamSchemas.id,
  resource: 'ANNOUNCEMENT',
  action: 'READ',
});

// PUT /api/announcements/[id] - Update an announcement (admin only)
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getCurrentUser(request);
    if (!user || user.role !== 'Admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const { id } = await params;
    const body = await request.json();
    const validation = updateAnnouncementSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        {
          error: 'Invalid request body',
          issues: validation.error.flatten().fieldErrors,
        },
        { status: 400 }
      );
    }

    const updateData: any = {};
    if (validation.data.title) updateData.title = validation.data.title;
    if (validation.data.content) updateData.content = validation.data.content;
    if (validation.data.date) updateData.date = new Date(validation.data.date);

    const announcement = await prisma.announcement.update({
      where: { id },
      data: updateData,
      include: {
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      message: 'Announcement updated successfully',
      announcement,
    });

  } catch (error) {
    if (error.code === 'P2025') {
      return NextResponse.json(
        { error: 'Announcement not found' },
        { status: 404 }
      );
    }
    console.error('Error updating announcement:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE /api/announcements/[id] - Delete an announcement (admin only)
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getCurrentUser(request);
    if (!user || user.role !== 'Admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const { id } = await params;

    await prisma.announcement.delete({
      where: { id },
    });

    return NextResponse.json({
      success: true,
      message: 'Announcement deleted successfully',
    });

  } catch (error) {
    if (error.code === 'P2025') {
      return NextResponse.json(
        { error: 'Announcement not found' },
        { status: 404 }
      );
    }
    console.error('Error deleting announcement:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

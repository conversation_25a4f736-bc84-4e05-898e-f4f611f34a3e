import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/middleware';
import { prisma } from '@/lib/prisma';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { id } = params;
    const body = await request.json();
    const { signature, approvalType } = body;

    const timesheet = await prisma.timesheet.findUnique({
      where: { id },
      include: { shift: { include: { job: true } } },
    });

    if (!timesheet) {
      return NextResponse.json(
        { error: 'Timesheet not found' },
        { status: 404 }
      );
    }

    if (approvalType === 'customer') {
      if (timesheet.status !== 'Pending') {
        return NextResponse.json(
          { error: 'Timesheet is not pending client approval' },
          { status: 400 }
        );
      }

      const hasPermission =
        user.role === 'Admin' ||
        (user.role === 'CrewChief' && timesheet.shift.crewChiefId === user.id) ||
        user.role === 'CompanyUser';

      if (!hasPermission) {
        return NextResponse.json(
          { error: 'Insufficient permissions' },
          { status: 403 }
        );
      }

      await prisma.timesheet.update({
        where: { id },
        data: {
          status: 'Submitted',
          companySignature: signature,
          companyApprovedAt: new Date(),
        },
      });

      const managers = await prisma.user.findMany({
        where: { role: 'Admin' },
        select: { id: true },
      });

      // Simplified notification logic
      console.log(`TODO: Notify ${managers.length} managers for final approval`);

      return NextResponse.json({
        success: true,
        message: 'Timesheet approved by client',
      });
    } else if (approvalType === 'manager') {
      if (timesheet.status !== 'Submitted') {
        return NextResponse.json(
          { error: 'Timesheet is not pending final approval' },
          { status: 400 }
        );
      }

      if (user.role !== 'Admin') {
        return NextResponse.json(
          { error: 'Only managers can provide final approval' },
          { status: 403 }
        );
      }

      await prisma.timesheet.update({
        where: { id },
        data: {
          status: 'Approved',
          managerSignature: signature,
          managerApprovedAt: new Date(),
        },
      });

      await prisma.shift.update({
        where: { id: timesheet.shiftId },
        data: { status: 'Completed' },
      });

      return NextResponse.json({
        success: true,
        message: 'Timesheet approved by manager',
      });
    } else {
      return NextResponse.json(
        { error: 'Invalid approval type' },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error('Error approving timesheet:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { id } = params;
    const body = await request.json();
    const { reason } = body;

    if (user.role !== 'Admin') {
      return NextResponse.json(
        { error: 'Only managers can reject timesheets' },
        { status: 403 }
      );
    }

    const timesheet = await prisma.timesheet.findUnique({ where: { id } });

    if (!timesheet) {
      return NextResponse.json(
        { error: 'Timesheet not found' },
        { status: 404 }
      );
    }

    if (
      timesheet.status !== 'Pending' &&
      timesheet.status !== 'Submitted'
    ) {
      return NextResponse.json(
        { error: 'Timesheet cannot be rejected in current status' },
        { status: 400 }
      );
    }

    await prisma.timesheet.update({
      where: { id },
      data: {
        status: 'Rejected',
        rejectionReason: reason,
      },
    });

    await prisma.shift.update({
      where: { id: timesheet.shiftId },
      data: { status: 'InProgress' },
    });

    return NextResponse.json({
      success: true,
      message: 'Timesheet rejected',
    });
  } catch (error) {
    console.error('Error rejecting timesheet:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

import nodemailer from 'nodemailer';
import { getEnvConfig, getFeatureFlags } from './env-validation';

// Create reusable transporter object using SMTP transport
function createTransporter() {
  const config = getEnvConfig();
  const features = getFeatureFlags(config);

  if (!features.emailEnabled) {
    throw new Error('Email configuration is not complete. Please set SMTP_HOST, SMTP_PORT, SMTP_USER, and SMTP_PASS environment variables.');
  }

  return nodemailer.createTransport({
    host: config.SMTP_HOST,
    port: parseInt(config.SMTP_PORT!),
    secure: parseInt(config.SMTP_PORT!) === 465, // true for 465, false for other ports
    auth: {
      user: config.SMTP_USER,
      pass: config.SMTP_PASS,
    },
  });
}

export async function sendPasswordResetEmail(email: string, resetLink: string): Promise<void> {
  try {
    const config = getEnvConfig();
    const features = getFeatureFlags(config);

    if (!features.emailEnabled) {
      // Fallback to console logging if email is not configured
      console.log(`
        ------------------------------------
        PASSWORD RESET EMAIL (Email not configured - showing in console)
        To: ${email}
        Subject: Password Reset Request

        You have requested a password reset.
        Please click on the following link to reset your password:

        ${resetLink}

        This link will expire in 1 hour.

        If you did not request this, please ignore this email.
        ------------------------------------
      `);
      return;
    }

    const transporter = createTransporter();

    const mailOptions = {
      from: `"HoliTime Support" <${config.SMTP_USER}>`,
      to: email,
      subject: 'Password Reset Request - HoliTime',
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Password Reset Request</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
            <h1 style="color: #2563eb; margin: 0; font-size: 24px;">Password Reset Request</h1>
          </div>

          <div style="background: white; padding: 20px; border-radius: 8px; border: 1px solid #e5e7eb;">
            <p>Hello,</p>

            <p>You have requested a password reset for your HoliTime account.</p>

            <p>Please click on the button below to reset your password:</p>

            <div style="text-align: center; margin: 30px 0;">
              <a href="${resetLink}"
                 style="background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: bold;">
                Reset Password
              </a>
            </div>

            <p>Or copy and paste this link into your browser:</p>
            <p style="word-break: break-all; background: #f3f4f6; padding: 10px; border-radius: 4px; font-family: monospace;">
              ${resetLink}
            </p>

            <p><strong>This link will expire in 1 hour.</strong></p>

            <p>If you did not request this password reset, please ignore this email. Your password will remain unchanged.</p>

            <hr style="border: none; border-top: 1px solid #e5e7eb; margin: 20px 0;">

            <p style="font-size: 14px; color: #6b7280;">
              Best regards,<br>
              The HoliTime Team
            </p>
          </div>
        </body>
        </html>
      `,
      text: `
        Password Reset Request - HoliTime

        Hello,

        You have requested a password reset for your HoliTime account.

        Please click on the following link to reset your password:
        ${resetLink}

        This link will expire in 1 hour.

        If you did not request this password reset, please ignore this email. Your password will remain unchanged.

        Best regards,
        The HoliTime Team
      `,
    };

    await transporter.sendMail(mailOptions);
    console.log(`Password reset email sent successfully to ${email}`);
  } catch (error) {
    console.error('Error sending password reset email:', error);
    throw new Error('Failed to send password reset email');
  }
}
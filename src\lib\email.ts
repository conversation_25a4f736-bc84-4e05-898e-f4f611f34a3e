// This is a placeholder for your email sending utility.
// In a real application, you would integrate with a service like SendGrid, Nodemailer, etc.

export async function sendPasswordResetEmail(email: string, resetLink: string): Promise<void> {
  console.log(`
    ------------------------------------
    PASSWORD RESET EMAIL
    To: ${email}
    Subject: Password Reset Request

    You have requested a password reset.
    Please click on the following link to reset your password:

    ${resetLink}

    This link will expire in 1 hour.

    If you did not request this, please ignore this email.
    ------------------------------------
  `);
  // In a real application, you would use an email sending library here.
  // Example with Nodemailer:
  /*
  const nodemailer = require('nodemailer');
  let transporter = nodemailer.createTransport({
    service: 'gmail', // or your SMTP service
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS
    }
  });

  let mailOptions = {
    from: '<EMAIL>',
    to: email,
    subject: 'Password Reset Request',
    html: `
      <p>You have requested a password reset.</p>
      <p>Please click on the following link to reset your password:</p>
      <a href="${resetLink}">${resetLink}</a>
      <p>This link will expire in 1 hour.</p>
      <p>If you did not request this, please ignore this email.</p>
    `
  };

  await transporter.sendMail(mailOptions);
  */
}
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { StopCircle, FileText } from 'lucide-react';
import { IShiftActionsProps } from './types';

const ShiftActions: React.FC<IShiftActionsProps> = ({ shift, onEndAllShifts, onFinalizeTimesheet, loading, isOnline }) => {
  const canEndAllShifts = shift.assignedPersonnel.some(w => w.status !== 'shift_ended');
  const canFinalizeTimesheet = shift.assignedPersonnel.every(w => w.status === 'shift_ended');

  return (
    <Card>
      <CardHeader>
        <CardTitle>Bulk Actions</CardTitle>
        <CardDescription>
          Perform actions on all workers at once
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-wrap gap-4">
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button
                variant="destructive"
                disabled={!isOnline || !canEndAllShifts || loading}
              >
                <StopCircle className="h-4 w-4 mr-2" />
                End All Shifts
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>End All Active Shifts?</AlertDialogTitle>
                <AlertDialogDescription>
                  This will end shifts for all workers who haven't completed their shifts yet. 
                  This action cannot be undone.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction onClick={onEndAllShifts}>
                  End All Shifts
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>

          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button
                disabled={!isOnline || !canFinalizeTimesheet || loading}
              >
                <FileText className="h-4 w-4 mr-2" />
                Finalize Timesheet
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Finalize Timesheet?</AlertDialogTitle>
                <AlertDialogDescription>
                  This will create a final timesheet for client approval. 
                  Make sure all workers have completed their shifts.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction onClick={onFinalizeTimesheet}>
                  Finalize Timesheet
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </CardContent>
    </Card>
  );
};

export default ShiftActions;

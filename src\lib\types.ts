import { z } from 'zod';

/**
 * Defines the roles a user can have within the system.
 * Based on the UserRole enum in `prisma/schema.prisma`.
 */
export const UserRoleSchema = z.enum([
  'Staff',
  'Admin',
  'CompanyUser',
  'CrewChief',
  'Employee',
]);
export type UserRole = z.infer<typeof UserRoleSchema>;

/**
 * Represents a client company.
 */
export const CompanySchema = z.object({
  id: z.string().cuid({ message: 'Invalid company ID' }),
  name: z.string().min(1, { message: 'Company name is required' }),
});
export type Company = z.infer<typeof CompanySchema>;

/**
 * Represents a user of the application.
 */
export const UserSchema = z.object({
  id: z.string().cuid({ message: 'Invalid user ID' }),
  name: z.string().min(1, { message: 'Name is required' }),
  email: z.string().email({ message: 'Invalid email address' }),
  role: UserRoleSchema,
  companyId: z.string().cuid({ message: 'Invalid company ID' }).nullable(),
});
export type User = z.infer<typeof UserSchema>;

/**
 * Represents a job or project.
 * This schema is based on a simplified view where a job has one crew chief and multiple workers directly associated.
 */
export const JobSchema = z.object({
  id: z.string().cuid({ message: 'Invalid job ID' }),
  name: z.string().min(1, { message: 'Job name is required' }),
  address: z.string().min(1, { message: 'Job address is required' }),
  crewChiefId: z.string().cuid({ message: 'Invalid crew chief ID' }),
  workerIds: z.array(z.string().cuid()).default([]),
});
export type Job = z.infer<typeof JobSchema>;

/**
 * Represents a single work entry for a user on a specific job.
 * This is a simplified representation of a work record, combining details from Shift, AssignedPersonnel, and TimeEntry.
 */
export const ShiftSchema = z.object({
  id: z.string().cuid({ message: 'Invalid shift ID' }),
  jobId: z.string().cuid({ message: 'Invalid job ID' }),
  userId: z.string().cuid({ message: 'Invalid user ID' }),
  clockIn: z.coerce.date(),
  clockOut: z.coerce.date().optional().nullable(),
  isFinalized: z.boolean().default(false),
});
export type Shift = z.infer<typeof ShiftSchema>;

# HoliTime Cloud Run Deployment Script (PowerShell)
# This script builds and deploys the HoliTime application to Google Cloud Run

param(
    [string]$ProjectId = $env:PROJECT_ID,
    [string]$Region = "us-central1",
    [string]$ServiceName = "holitime"
)

# Configuration
if (-not $ProjectId -or $ProjectId -eq "your-project-id") {
    Write-Host "❌ Please set PROJECT_ID environment variable or pass -ProjectId parameter" -ForegroundColor Red
    Write-Host "Example: .\deploy-cloud-run.ps1 -ProjectId elated-fabric-460119-t3" -ForegroundColor Yellow
    exit 1
}

$ImageName = "gcr.io/$ProjectId/$ServiceName"

# Functions
function Write-Step {
    param([string]$Message)
    Write-Host "==> $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "✓ $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "⚠ $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "✗ $Message" -ForegroundColor Red
}

function Test-Prerequisites {
    Write-Step "Checking prerequisites..."
    
    # Check if gcloud is installed
    try {
        $null = Get-Command gcloud -ErrorAction Stop
    }
    catch {
        Write-Error "gcloud CLI is not installed. Please install it first."
        exit 1
    }
    
    # Check if docker is installed
    try {
        $null = Get-Command docker -ErrorAction Stop
    }
    catch {
        Write-Error "Docker is not installed. Please install it first."
        exit 1
    }
    
    # Check if user is authenticated
    $authCheck = gcloud auth list --filter=status:ACTIVE --format="value(account)" 2>$null
    if (-not $authCheck) {
        Write-Error "Not authenticated with gcloud. Run 'gcloud auth login' first."
        exit 1
    }
    
    Write-Success "Prerequisites check passed"
}

function Initialize-Project {
    Write-Step "Setting up Google Cloud project..."
    
    # Set the project
    gcloud config set project $ProjectId
    
    # Enable required APIs
    Write-Step "Enabling required APIs..."
    gcloud services enable cloudbuild.googleapis.com
    gcloud services enable run.googleapis.com
    gcloud services enable containerregistry.googleapis.com
    gcloud services enable secretmanager.googleapis.com
    
    Write-Success "Project setup completed"
}

function New-Secrets {
    Write-Step "Creating secrets in Secret Manager..."
    
    $secrets = @(
        "DATABASE_URL",
        "NEXTAUTH_SECRET", 
        "JWT_SECRET",
        "GOOGLE_CLIENT_ID",
        "GOOGLE_CLIENT_SECRET"
    )
    
    foreach ($secret in $secrets) {
        $secretExists = gcloud secrets describe $secret 2>$null
        if ($secretExists) {
            Write-Warning "Secret $secret already exists, skipping..."
        }
        else {
            $secretValue = Read-Host "Enter value for $secret" -AsSecureString
            $plainValue = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($secretValue))
            $plainValue | gcloud secrets create $secret --data-file=-
            Write-Success "Created secret: $secret"
        }
    }
}

function Build-AndPushImage {
    Write-Step "Building and pushing Docker image..."
    
    # Build the image
    docker build -t "$ImageName`:latest" .
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Docker build failed"
        exit 1
    }
    
    # Configure Docker to use gcloud as a credential helper
    gcloud auth configure-docker
    
    # Push the image
    docker push "$ImageName`:latest"
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Docker push failed"
        exit 1
    }
    
    Write-Success "Image built and pushed successfully"
}

function Deploy-ToCloudRun {
    Write-Step "Deploying to Cloud Run..."
    
    $deployArgs = @(
        "run", "deploy", $ServiceName,
        "--image", "$ImageName`:latest",
        "--region", $Region,
        "--platform", "managed",
        "--allow-unauthenticated",
        "--port", "3000",
        "--memory", "2Gi",
        "--cpu", "2",
        "--max-instances", "1",
        "--min-instances", "1",
        "--concurrency", "80",
        "--timeout", "300",
        "--set-env-vars", "NODE_ENV=production,NEXT_TELEMETRY_DISABLED=1",
        "--set-secrets", "DATABASE_URL=DATABASE_URL:latest,NEXTAUTH_SECRET=NEXTAUTH_SECRET:latest,JWT_SECRET=JWT_SECRET:latest,GOOGLE_CLIENT_ID=GOOGLE_CLIENT_ID:latest,GOOGLE_CLIENT_SECRET=GOOGLE_CLIENT_SECRET:latest"
    )
    
    & gcloud @deployArgs
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Cloud Run deployment failed"
        exit 1
    }
    
    Write-Success "Deployment completed successfully"
}

function Show-MigrationInstructions {
    Write-Step "Database Migration Instructions"
    
    Write-Warning "You may need to run database migrations manually:"
    Write-Host "1. Connect to your database" -ForegroundColor Cyan
    Write-Host "2. Run: npx prisma migrate deploy" -ForegroundColor Cyan
    Write-Host "3. Or use Cloud Run Jobs to run migrations" -ForegroundColor Cyan
    
    Write-Success "Migration instructions provided"
}

function Show-DeploymentInfo {
    Write-Step "Deployment Information"
    
    $serviceUrl = gcloud run services describe $ServiceName --region=$Region --format="value(status.url)"
    
    Write-Host "🎉 Deployment completed successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "Service URL: $serviceUrl" -ForegroundColor Cyan
    Write-Host "Project ID: $ProjectId" -ForegroundColor Cyan
    Write-Host "Region: $Region" -ForegroundColor Cyan
    Write-Host "Service Name: $ServiceName" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Next steps:" -ForegroundColor Yellow
    Write-Host "1. Update your NEXTAUTH_URL environment variable to: $serviceUrl"
    Write-Host "2. Configure your Google OAuth redirect URIs to include: $serviceUrl/api/auth/callback/google"
    Write-Host "3. Test your application at: $serviceUrl"
    Write-Host "4. Monitor logs with: gcloud logs tail --service=$ServiceName"
}

# Main execution
function Main {
    Write-Host "HoliTime Cloud Run Deployment" -ForegroundColor Blue
    Write-Host "==================================" -ForegroundColor Blue
    
    Test-Prerequisites
    Initialize-Project
    
    # Ask if user wants to create secrets
    $createSecrets = Read-Host "Do you want to create/update secrets? (y/n)"
    if ($createSecrets -match "^[Yy]$") {
        New-Secrets
    }
    
    Build-AndPushImage
    Deploy-ToCloudRun
    Show-MigrationInstructions
    Show-DeploymentInfo
}

# Run the main function
try {
    Main
}
catch {
    Write-Error "Deployment failed: $_"
    exit 1
}

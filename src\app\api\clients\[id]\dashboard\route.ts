import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { withAuthApi } from '@/lib/auth-api';
import { UserRole } from '@prisma/client';

type RequestContext = {
  params: {
    id: string;
  };
};

async function handler(req: Request, { params }: RequestContext) {
  const { id } = params;

  if (!id || typeof id !== 'string') {
    return NextResponse.json({ error: 'Client ID is required' }, { status: 400 });
  }

  try {
    const jobs = await prisma.job.findMany({
      where: { companyId: id },
      include: {
        shifts: {
          include: {
            assignedPersonnel: true,
          },
        },
      },
      orderBy: {
        start_date: 'desc',
      },
    });

    const activeJobs = jobs.filter((job: any) => job.status === 'InProgress');
    const upcomingShifts = jobs.flatMap((job: any) => job.shifts).filter((shift: any) => shift.status === 'Pending' || shift.status === 'Confirmed');
    const completedShifts = jobs.flatMap((job: any) => job.shifts).filter((shift: any) => shift.status === 'Completed');

    return NextResponse.json({
      activeJobsCount: activeJobs.length,
      upcomingShiftsCount: upcomingShifts.length,
      completedShiftsCount: completedShifts.length,
      recentJobs: jobs.slice(0, 5),
      upcomingShifts: upcomingShifts.slice(0, 5),
    });
  } catch (error) {
    console.error(`Error fetching client dashboard data for ${id}:`, error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export const GET = withAuthApi(
  handler,
  (role: UserRole) => role === 'Admin' || role === 'CompanyUser'
);

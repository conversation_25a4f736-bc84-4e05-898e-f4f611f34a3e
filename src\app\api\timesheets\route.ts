import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/middleware';
import { prisma } from '@/lib/prisma';
import { UserRole } from '@prisma/client';


export async function GET(request: NextRequest) {
  const user = await getCurrentUser(request);
  const isAuthorized = user.role === UserRole.Admin || user.role === UserRole.CrewChief;
  if (!user || !isAuthorized) {
    return NextResponse.json(
      { error: 'Authentication required' },
      { status: 401 }
    );
  }

  const { searchParams } = new URL(request.url);
  const shiftId = searchParams.get('shiftId');

  const where: any = {
    shiftId: shiftId ?? undefined,
  };

  if (user.role === UserRole.CrewChief) {
    // This logic needs to be adjusted based on how CrewChief is associated with a shift.
    // Assuming a crew chief is assigned to a shift via AssignedPersonnel
    where.shift = {
      assignedPersonnel: {
        some: {
          userId: user.id,
          // Assuming there's a way to identify the crew chief role here
        },
      },
    };
  }

  try {
    const timesheets = await prisma.timesheet.findMany({
      where,
      include: {
        shift: {
          include: {
            job: {
              include: {
                company: true,
              },
            },
            assignedPersonnel: {
              include: {
                user: true,
                timeEntries: true,
              },
            },
          },
        },
      },
      orderBy: {
        shift: {
          date: 'desc',
        },
      },
    });

    return NextResponse.json({
      success: true,
      timesheets,
    });
  } catch (error) {
    console.error('Error getting timesheets:', error);
    const errorMessage =
      error instanceof Error ? error.message : 'An unexpected error occurred';
    return NextResponse.json(
      { error: 'Internal server error', details: errorMessage },
      { status: 500 }
    );
  }
}

import { z } from 'zod';
import { TimesheetStatus } from '@prisma/client';

const createTimesheetSchema = z.object({
  shiftId: z.string().min(1, { message: 'Shift ID is required' }),
});

export async function POST(request: NextRequest) {
  const user = await getCurrentUser(request);
  const isAuthorized = user.role === UserRole.Admin || user.role === UserRole.CrewChief;
  if (!user || !isAuthorized) {
    return NextResponse.json(
      { error: 'Authentication required' },
      { status: 401 }
    );
  }

  try {
    const body = await request.json();
    const validation = createTimesheetSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        {
          error: 'Invalid request body',
          issues: validation.error.flatten().fieldErrors,
        },
        { status: 400 }
      );
    }

    const { shiftId } = validation.data;

    // Check if a timesheet for this shift already exists
    const existingTimesheet = await prisma.timesheet.findUnique({
      where: { shiftId },
    });

    if (existingTimesheet) {
      return NextResponse.json(
        { error: 'A timesheet for this shift already exists' },
        { status: 409 }
      );
    }

    // Verify the shift exists
    const shift = await prisma.shift.findUnique({
      where: { id: shiftId },
    });

    if (!shift) {
      return NextResponse.json(
        { error: 'Shift not found' },
        { status: 404 }
      );
    }

    const newTimesheet = await prisma.timesheet.create({
      data: {
        shiftId,
        status: TimesheetStatus.Pending,
      },
    });

    return NextResponse.json({
      success: true,
      timesheet: newTimesheet,
    });
  } catch (error) {
    console.error('Error creating timesheet:', error);
    const errorMessage =
      error instanceof Error ? error.message : 'An unexpected error occurred';
    return NextResponse.json(
      { error: 'Internal server error', details: errorMessage },
      { status: 500 }
    );
  }
}

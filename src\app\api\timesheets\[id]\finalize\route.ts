import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma';
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth-config"
import { TimesheetStatus, UserRole } from '@prisma/client';

export async function POST(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (session?.user.role !== UserRole.Admin) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const { id } = params
    const { signature } = await req.json()

    if (!signature) {
      return new NextResponse("Signature is required", { status: 400 })
    }

    const timesheet = await prisma.timesheet.findUnique({ where: { id } });

    if (!timesheet) {
      return new NextResponse("Timesheet not found", { status: 404 })
    }

    if (timesheet.status !== TimesheetStatus.Submitted) {
      return new NextResponse("Timesheet is not awaiting final approval", { status: 400 })
    }

    const updatedTimesheet = await prisma.timesheet.update({
      where: { id },
      data: {
        status: TimesheetStatus.Approved,
        managerSignature: signature,
        managerApprovedAt: new Date(),
      },
    });

    return NextResponse.json(updatedTimesheet)
  } catch (error) {
    console.error("[TIMESHEET_FINALIZE]", error)
    return new NextResponse("Internal Error", { status: 500 })
  }
}

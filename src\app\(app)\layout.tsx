"use client"

import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  Building2,
  CalendarClock,
  ClipboardCheck,
  FileText,
  Hand,
  LayoutDashboard,
  Settings,
  Upload,
  Users,
  ChevronDown,
  ChevronUp,
  Menu,
  X,
} from 'lucide-react';
import { UserNav } from '@/components/user-nav';
import { useScrollDirection } from '@/hooks/use-scroll-direction';
import { useUser } from '@/hooks/use-user';
import { AuthGuard } from '@/components/auth-guard';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { UserRole } from '@prisma/client';

function NavLink({ href, children, className }: { href: string; children: React.ReactNode, className?: string }) {
  const pathname = usePathname();
  const isActive = pathname === href || (href !== '/dashboard' && pathname.startsWith(href));

  return (
    <Link
      href={href}
      className={cn(
        'flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary',
        { 'bg-muted text-primary': isActive },
        className
      )}
    >
      {children}
    </Link>
  );
}

function NavContent() {
  const { user } = useUser();

  const navLinks = [
    { href: '/dashboard', label: 'Dashboard', icon: LayoutDashboard, roles: [UserRole.Admin, UserRole.CompanyUser, UserRole.Staff, UserRole.CrewChief, UserRole.Employee] },
    { href: '/shifts', label: "Today's Shifts", icon: CalendarClock, roles: [UserRole.Admin, UserRole.CrewChief, UserRole.Staff, UserRole.CompanyUser, UserRole.Employee] },
    { href: '/timesheets', label: 'Timesheets', icon: ClipboardCheck, roles: [UserRole.Admin, UserRole.CrewChief, UserRole.CompanyUser, UserRole.Employee] },
    { href: '/clients', label: 'Clients', icon: Building2, roles: [UserRole.Admin] },
    { href: '/employees', label: 'Employees', icon: Users, roles: [UserRole.Admin] },
    { href: '/documents', label: 'Documents', icon: FileText, roles: [UserRole.Admin, UserRole.CompanyUser, UserRole.Staff, UserRole.CrewChief, UserRole.Employee] },
    { href: '/import', label: 'Data Import', icon: Upload, roles: [UserRole.Admin] },
    { href: '/admin', label: 'Admin', icon: Settings, roles: [UserRole.Admin] },
  ];

  const filteredNavLinks = navLinks.filter(link => user?.role && link.roles.includes(user.role as UserRole));

  return (
    <nav className="grid items-start gap-2 text-sm font-medium">
      {filteredNavLinks.map(link => (
        <NavLink key={link.href} href={link.href}>
          <link.icon className="h-4 w-4" />
          {link.label}
        </NavLink>
      ))}
    </nav>
  );
}

function BottomNav() {
  const { user } = useUser();
  const scrollDirection = useScrollDirection();
  const [manualHidden, setManualHidden] = useState(false);

  const isVisible = !manualHidden && scrollDirection !== 'down';

  const navLinks = [
    { href: '/dashboard', label: 'Home', icon: LayoutDashboard, roles: [UserRole.Admin, UserRole.CompanyUser, UserRole.Staff, UserRole.CrewChief, UserRole.Employee] },
    { href: '/shifts', label: 'Shifts', icon: CalendarClock, roles: [UserRole.Admin, UserRole.CrewChief, UserRole.Staff, UserRole.Employee] },
    { href: '/timesheets', label: 'Timesheets', icon: ClipboardCheck, roles: [UserRole.Admin, UserRole.CrewChief, UserRole.Employee] },
    { href: '/documents', label: 'Docs', icon: FileText, roles: [UserRole.Admin, UserRole.CompanyUser, UserRole.Staff, UserRole.CrewChief, UserRole.Employee] },
  ];
  
  const filteredNavLinks = navLinks.filter(link => user?.role && link.roles.includes(user.role as UserRole));

  return (
    <nav
      className={cn(
        'md:hidden fixed bottom-0 left-0 right-0 z-50 bg-background border-t transition-transform duration-200 ease-in-out',
        { 'translate-y-full': !isVisible }
      )}
    >
      <div className="flex justify-around items-center h-16">
        {filteredNavLinks.map(link => (
          <NavLink key={link.href} href={link.href} className="flex-col h-auto gap-1 px-2 py-1">
            <link.icon className="h-5 w-5" />
            <span className="text-xs">{link.label}</span>
          </NavLink>
        ))}
        <Button onClick={() => setManualHidden(prev => !prev)} variant="ghost" size="icon">
          {manualHidden ? <ChevronUp className="h-5 w-5" /> : <ChevronDown className="h-5 w-5" />}
        </Button>
      </div>
    </nav>
  );
}

export default function AppLayout({ children }: { children: React.ReactNode }) {
  const [mobileNavOpen, setMobileNavOpen] = useState(false);

  return (
    <AuthGuard>
      <div className="grid min-h-screen w-full md:grid-cols-[220px_1fr] lg:grid-cols-[280px_1fr]">
        <div className={cn(
          "fixed inset-0 z-40 md:relative md:translate-x-0 md:block bg-background border-r transition-transform duration-300 ease-in-out",
          mobileNavOpen ? "translate-x-0" : "-translate-x-full"
        )}>
          <div className="flex h-full max-h-screen flex-col gap-2">
            <div className="flex h-14 items-center border-b px-4 lg:h-[60px] lg:px-6">
              <Link href="/dashboard" className="flex items-center gap-2 font-semibold">
                <Hand className="h-6 w-6 text-primary" />
                <span className="">Hands On Labor</span>
              </Link>
              <Button variant="ghost" size="icon" className="ml-auto h-8 w-8 md:hidden" onClick={() => setMobileNavOpen(false)}>
                <X className="h-4 w-4" />
                <span className="sr-only">Close navigation</span>
              </Button>
            </div>
            <div className="flex-1 overflow-auto py-2">
              <NavContent />
            </div>
          </div>
        </div>
        <div className="flex flex-col">
          <header className="flex h-14 items-center gap-4 border-b bg-background px-4 lg:h-[60px] lg:px-6 sticky top-0 z-30">
            <Button variant="outline" size="icon" className="h-8 w-8 md:hidden" onClick={() => setMobileNavOpen(true)}>
              <Menu className="h-4 w-4" />
              <span className="sr-only">Open navigation</span>
            </Button>
            <div className="w-full flex-1" />
            <UserNav />
          </header>
          <main className="flex flex-1 flex-col gap-4 p-4 lg:gap-6 lg:p-6">
            {children}
          </main>
          <BottomNav />
        </div>
      </div>
    </AuthGuard>
  );
}

"use client"

import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  Building2,
  CalendarClock,
  ClipboardCheck,
  FileText,
  Hand,
  LayoutDashboard,
  Settings,
  Upload,
  Users,
  ChevronDown,
  ChevronUp,
  Menu,
  X,
} from 'lucide-react';
import { UserNav } from '@/components/user-nav';
import { useScrollDirection } from '@/hooks/use-scroll-direction';
import { useUser } from '@/hooks/use-user';
import { AuthGuard } from '@/components/auth-guard';
import { NavigationGuard } from '@/components/security/navigation-guard';
import { cn } from '@/lib/utils';
import { UserRole } from '@prisma/client';

export default function AppLayout({ children }: { children: React.ReactNode }) {
  const [mobileNavOpen, setMobileNavOpen] = useState(false);

  return (
    <AuthGuard>
      <NavigationGuard>
        <div className="min-h-screen bg-background">
          {/* Mobile Header */}
          <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 md:hidden">
            <div className="flex h-14 items-center px-4">
              <button 
                className="btn btn-outline mr-2 h-8 w-8 p-0"
                onClick={() => setMobileNavOpen(true)}
              >
                <Menu className="h-4 w-4" />
              </button>
              <Link href="/dashboard" className="flex items-center gap-2 font-semibold">
                <Hand className="h-6 w-6 text-primary" />
                <span>Hands On Labor</span>
              </Link>
              <div className="ml-auto">
                <UserNav />
              </div>
            </div>
          </header>

          <div className="flex">
            {/* Desktop Sidebar */}
            <aside className="hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0">
              <div className="flex flex-col flex-grow pt-5 bg-background border-r overflow-y-auto">
                <div className="flex items-center flex-shrink-0 px-4">
                  <Link href="/dashboard" className="flex items-center gap-2 font-semibold">
                    <Hand className="h-6 w-6 text-primary" />
                    <span>Hands On Labor</span>
                  </Link>
                </div>
                <div className="mt-8 flex-grow flex flex-col">
                  <nav className="flex-1 px-2 space-y-1">
                    {/* Navigation items */}
                  </nav>
                </div>
              </div>
            </aside>

            {/* Mobile Sidebar Overlay */}
            {mobileNavOpen && (
              <div className="fixed inset-0 z-50 md:hidden">
                <div className="fixed inset-0 bg-black/50" onClick={() => setMobileNavOpen(false)} />
                <div className="fixed left-0 top-0 h-full w-64 bg-background border-r">
                  <div className="flex h-14 items-center justify-between px-4 border-b">
                    <Link href="/dashboard" className="flex items-center gap-2 font-semibold">
                      <Hand className="h-6 w-6 text-primary" />
                      <span>Hands On Labor</span>
                    </Link>
                    <button 
                      className="btn btn-outline h-8 w-8 p-0"
                      onClick={() => setMobileNavOpen(false)}
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                  <nav className="p-4 space-y-2">
                    {/* Navigation items */}
                  </nav>
                </div>
              </div>
            )}

            {/* Main Content */}
            <main className="flex-1 md:ml-64">
              <div className="py-6">
                {children}
              </div>
            </main>
          </div>
        </div>
      </NavigationGuard>
    </AuthGuard>
  );
}




# Google Cloud Build configuration for deploying to Cloud Run
steps:
  # 1. Build the Docker image
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'build'
      - '--tag=gcr.io/$PROJECT_ID/holitime:$COMMIT_SHA'
      - '--build-arg'
      - 'NEXTAUTH_SECRET=${_NEXTAUTH_SECRET}'
      - '--build-arg'
      - 'JWT_SECRET=${_JWT_SECRET}'
      - '.'
    id: 'Build'

  # 2. Push the image to Google Container Registry
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/holitime:$COMMIT_SHA']
    id: 'Push'

  # 3. Deploy to Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: gcloud
    args:
      - 'run'
      - 'deploy'
      - 'holitime'
      - '--image=gcr.io/$PROJECT_ID/holitime:$COMMIT_SHA'
      - '--region=us-west2'
      - '--platform=managed'
      - '--allow-unauthenticated'
      - '--port=3000'
      - '--memory=1Gi'
      - '--cpu=1'
      - '--max-instances=5'
      - '--set-env-vars=NODE_ENV=production,NEXT_TELEMETRY_DISABLED=1'
      - '--set-secrets=DATABASE_URL=DATABASE_URL:latest,NEXTAUTH_SECRET=NEXTAUTH_SECRET:latest,GOOGLE_CLIENT_ID=GOOGLE_CLIENT_ID:latest,GOOGLE_CLIENT_SECRET=GOOGLE_CLIENT_SECRET:latest,GOOGLE_API_KEY=GOOGLE_API_KEY:latest,JWT_SECRET=JWT_SECRET:latest,SMTP_HOST=SMTP_HOST:latest,SMTP_PORT=SMTP_PORT:latest,SMTP_USER=SMTP_USER:latest,SMTP_PASS=SMTP_PASS:latest'
      - '--add-cloudsql-instances=holitime-465520:us-west2:holitime-db'
    id: 'Deploy'

  # 4. Run Prisma migrations after deployment
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: gcloud
    args:
      - 'run'
      - 'jobs'
      - 'execute'
      - 'holitime-migrate'
      - '--region=us-west2'
      - '--wait'
    id: 'Migrate Database'

# Store the image in GCR
images:
  - 'gcr.io/$PROJECT_ID/holitime:$COMMIT_SHA'

# Improve build performance with caching
options:
  machineType: 'E2_HIGHCPU_8'
  diskSizeGb: '100'

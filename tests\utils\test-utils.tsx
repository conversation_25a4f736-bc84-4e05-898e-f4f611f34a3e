/**
 * Test Utilities
 * Provides common utilities and helpers for testing React components and hooks
 */

import React, { ReactElement } from 'react'
import { render, RenderOptions, RenderResult } from '@testing-library/react'
import { SessionProvider } from 'next-auth/react'
import { QueryProvider } from '@/providers/query-provider'
import { UserRole } from '@prisma/client'

// Mock session data
export interface MockSession {
  user: {
    id: string
    name: string
    email: string
    role: UserRole
    companyId?: string
    avatarUrl?: string
  }
  expires: string
}

// Test wrapper component
interface TestWrapperProps {
  children: React.ReactNode
  session?: MockSession | null
}

function TestWrapper({ children, session = null }: TestWrapperProps) {
  return (
    <SessionProvider session={session}>
      <QueryProvider>
        {children}
      </QueryProvider>
    </SessionProvider>
  )
}

// Custom render function
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  session?: MockSession | null
}

export function renderWithProviders(
  ui: ReactElement,
  options: CustomRenderOptions = {}
): RenderResult {
  const { session, ...renderOptions } = options

  const Wrapper = ({ children }: { children: React.ReactNode }) => (
    <TestWrapper session={session}>{children}</TestWrapper>
  )

  return render(ui, { wrapper: Wrapper, ...renderOptions })
}

// Mock data factories
export const mockUsers = {
  admin: {
    id: 'admin-1',
    name: 'Admin User',
    email: '<EMAIL>',
    role: 'Admin' as UserRole,
    isActive: true,
    crewChiefEligible: false,
    forkOperatorEligible: false,
    certifications: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  
  crewChief: {
    id: 'crew-chief-1',
    name: 'Crew Chief',
    email: '<EMAIL>',
    role: 'CrewChief' as UserRole,
    isActive: true,
    crewChiefEligible: true,
    forkOperatorEligible: false,
    certifications: ['Safety Training'],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  
  employee: {
    id: 'employee-1',
    name: 'Employee User',
    email: '<EMAIL>',
    role: 'Employee' as UserRole,
    isActive: true,
    crewChiefEligible: false,
    forkOperatorEligible: true,
    certifications: ['Fork Operator'],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  
  companyUser: {
    id: 'company-user-1',
    name: 'Company User',
    email: '<EMAIL>',
    role: 'CompanyUser' as UserRole,
    companyId: 'company-1',
    isActive: true,
    crewChiefEligible: false,
    forkOperatorEligible: false,
    certifications: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
}

export const mockCompanies = {
  acmeCorp: {
    id: 'company-1',
    name: 'Acme Corporation',
    contactPerson: 'John Doe',
    contactEmail: '<EMAIL>',
    contactPhone: '555-0123',
    address: '123 Main St, City, State 12345',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  
  techCorp: {
    id: 'company-2',
    name: 'Tech Corp',
    contactPerson: 'Jane Smith',
    contactEmail: '<EMAIL>',
    contactPhone: '555-0456',
    address: '456 Tech Ave, City, State 12345',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
}

export const mockJobs = {
  constructionJob: {
    id: 'job-1',
    name: 'Construction Project',
    description: 'Building construction project',
    status: 'Active',
    startDate: new Date().toISOString(),
    endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
    location: 'Construction Site A',
    budget: '$50,000',
    notes: 'High priority project',
    isCompleted: false,
    companyId: 'company-1',
    company: mockCompanies.acmeCorp,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  
  maintenanceJob: {
    id: 'job-2',
    name: 'Maintenance Work',
    description: 'Regular maintenance tasks',
    status: 'Pending',
    startDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
    endDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(),
    location: 'Office Building B',
    budget: '$10,000',
    notes: 'Routine maintenance',
    isCompleted: false,
    companyId: 'company-2',
    company: mockCompanies.techCorp,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
}

export const mockShifts = {
  morningShift: {
    id: 'shift-1',
    jobId: 'job-1',
    job: mockJobs.constructionJob,
    date: new Date().toISOString().split('T')[0],
    startTime: new Date().toISOString(),
    endTime: new Date(Date.now() + 8 * 60 * 60 * 1000).toISOString(),
    location: 'Construction Site A',
    notes: 'Morning shift',
    requestedWorkers: 5,
    status: 'Active',
    assignedPersonnel: [],
    workerRequirements: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  
  eveningShift: {
    id: 'shift-2',
    jobId: 'job-1',
    job: mockJobs.constructionJob,
    date: new Date().toISOString().split('T')[0],
    startTime: new Date(Date.now() + 8 * 60 * 60 * 1000).toISOString(),
    endTime: new Date(Date.now() + 16 * 60 * 60 * 1000).toISOString(),
    location: 'Construction Site A',
    notes: 'Evening shift',
    requestedWorkers: 3,
    status: 'Pending',
    assignedPersonnel: [],
    workerRequirements: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
}

// Mock API responses
export const mockApiResponses = {
  success: (data: any) => ({
    success: true,
    data,
    timestamp: new Date().toISOString(),
  }),
  
  error: (error: string, status = 400) => ({
    success: false,
    error,
    timestamp: new Date().toISOString(),
    status,
  }),
  
  paginated: (data: any[], total = data.length) => ({
    success: true,
    data,
    pagination: {
      total,
      limit: 20,
      offset: 0,
      hasMore: false,
    },
    timestamp: new Date().toISOString(),
  }),
}

// Mock fetch responses
export function mockFetch(response: any, ok = true, status = 200) {
  return jest.fn().mockResolvedValue({
    ok,
    status,
    json: jest.fn().mockResolvedValue(response),
    text: jest.fn().mockResolvedValue(JSON.stringify(response)),
  })
}

// Mock session helpers
export function createMockSession(user = mockUsers.employee): MockSession {
  return {
    user: {
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role,
      companyId: user.companyId,
      avatarUrl: user.avatarUrl,
    },
    expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
  }
}

// Test helpers
export const testHelpers = {
  // Wait for async operations
  waitFor: (ms = 0) => new Promise(resolve => setTimeout(resolve, ms)),
  
  // Create form data
  createFormData: (data: Record<string, any>) => {
    const formData = new FormData()
    Object.entries(data).forEach(([key, value]) => {
      formData.append(key, value)
    })
    return formData
  },
  
  // Mock file
  createMockFile: (name = 'test.txt', type = 'text/plain', content = 'test content') => {
    return new File([content], name, { type })
  },
  
  // Mock event
  createMockEvent: (overrides = {}) => ({
    preventDefault: jest.fn(),
    stopPropagation: jest.fn(),
    target: { value: '' },
    currentTarget: { value: '' },
    ...overrides,
  }),
  
  // Mock router
  createMockRouter: (overrides = {}) => ({
    push: jest.fn(),
    replace: jest.fn(),
    prefetch: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
    pathname: '/',
    query: {},
    asPath: '/',
    ...overrides,
  }),
}

// Custom matchers
expect.extend({
  toHaveBeenCalledWithApiError(received, expectedError) {
    const pass = received.mock.calls.some(call => 
      call[0] && call[0].error === expectedError
    )
    
    return {
      message: () => 
        pass 
          ? `Expected not to be called with API error "${expectedError}"`
          : `Expected to be called with API error "${expectedError}"`,
      pass,
    }
  },
})

// Re-export everything from testing-library
export * from '@testing-library/react'
export { renderWithProviders as render }

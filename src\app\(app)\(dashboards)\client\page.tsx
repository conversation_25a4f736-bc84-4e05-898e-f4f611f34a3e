'use client';

import { useUser } from '@/hooks/use-user';
import { useApi } from '@/hooks/use-api';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Calendar, Users, Briefcase } from 'lucide-react';
import { Job, Shift } from '@prisma/client';

type DashboardData = {
  activeJobsCount: number;
  upcomingShiftsCount: number;
  completedShiftsCount: number;
  recentJobs: Job[];
  upcomingShifts: (Shift & { job: Job })[];
};

export default function ClientDashboard() {
  const { user } = useUser();
  const { data, loading, error } = useApi<DashboardData>(
    user?.companyId ? `/api/clients/${user.companyId}/dashboard` : null
  );

  if (loading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div className="text-red-500">Error loading dashboard data.</div>;
  }

  return (
    <div className="space-y-6">
      <header>
        <h1 className="text-3xl font-bold">Welcome, {user?.name}!</h1>
        <p className="text-muted-foreground">{user?.companyName}</p>
      </header>

      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Jobs</CardTitle>
            <Briefcase className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data?.activeJobsCount}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Upcoming Shifts</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data?.upcomingShiftsCount}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed Shifts</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data?.completedShiftsCount}</div>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Recent Jobs</CardTitle>
          </CardHeader>
          <CardContent>
            {data?.recentJobs && data.recentJobs.length > 0 ? (
              <div className="space-y-4">
                {data.recentJobs.map((job) => (
                  <div key={job.id} className="flex items-center justify-between">
                    <div>
                      <p className="font-semibold">{job.name}</p>
                      <p className="text-sm text-muted-foreground">{job.description}</p>
                    </div>
                    <Badge>{job.status}</Badge>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-center text-muted-foreground py-8">No recent jobs found</p>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Upcoming Shifts</CardTitle>
          </CardHeader>
          <CardContent>
            {data?.upcomingShifts && data.upcomingShifts.length > 0 ? (
              <div className="space-y-4">
                {data.upcomingShifts.map((shift) => (
                  <div key={shift.id} className="flex items-center justify-between">
                    <div>
                      <p className="font-semibold">{shift.job.name}</p>
                      <p className="text-sm text-muted-foreground">
                        {new Date(shift.date).toLocaleDateString()}
                      </p>
                    </div>
                    <Badge>{shift.status}</Badge>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-center text-muted-foreground py-8">No upcoming shifts</p>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

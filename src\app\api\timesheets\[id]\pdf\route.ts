import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/middleware';
import { prisma } from '@/lib/prisma';

// GET /api/timesheets/[id]/pdf - Download PDF from database
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { id } = params;

    const timesheet = await prisma.timesheet.findUnique({
      where: { id },
      include: {
        shift: {
          include: {
            job: true,
          },
        },
      },
    });

    if (!timesheet) {
      return NextResponse.json(
        { error: 'Timesheet not found' },
        { status: 404 }
      );
    }

    // Check permissions
    const hasAccess =
      user.role === 'Admin' ||
      user.id === timesheet.shift.crewChiefId ||
      (user.role === 'CompanyUser' && user.companyId === timesheet.shift.job.companyId);

    if (!hasAccess) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    // Check if PDF URL exists
    if (!timesheet.pdfUrl) {
      return NextResponse.json(
        { error: 'PDF not available for this timesheet.' },
        { status: 404 }
      );
    }

    // Redirect to the Google Drive URL
    return NextResponse.redirect(timesheet.pdfUrl, 307);
  } catch (error) {
    console.error('Error generating timesheet PDF:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

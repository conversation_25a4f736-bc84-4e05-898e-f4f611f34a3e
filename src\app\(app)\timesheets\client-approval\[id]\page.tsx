'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { TimesheetDetails } from '@/components/timesheet-details'
import SignatureCaptureModal from '@/components/signature-capture-modal'
import { Button } from '@/components/ui/button'
import { toast } from '@/hooks/use-toast'
import { TimesheetStatusIndicator } from '@/components/timesheet-status-indicator'
import { api } from '@/lib/api'
import { TimesheetDetails as TimesheetDetailsType } from '@/lib/types'

export default function ClientApprovalPage() {
  const { id } = useParams()
  const [showSignatureModal, setShowSignatureModal] = useState(false)

  const [timesheet, setTimesheet] = useState<TimesheetDetailsType | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const fetchTimesheet = async () => {
      try {
        const res = await api.get(`/api/timesheets?id=${id}`)
        if (!res.ok) throw new Error('Failed to fetch timesheet')
        setTimesheet(await res.json())
      } catch (error) {
        console.error('Error fetching timesheet:', error)
      } finally {
        setIsLoading(false)
      }
    }
    fetchTimesheet()
  }, [id])

  const handleSignatureSubmit = async (signatureData: string) => {
    try {
      const res = await api.post('/api/timesheets/client-approval', {
        timesheetId: id,
        signatureData
      })

      if (!res.ok) throw new Error('Failed to submit approval')

      toast({
        title: 'Success',
        description: 'Timesheet approved successfully',
        variant: 'default'
      })
      setShowSignatureModal(false)
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to approve timesheet. Please try again.',
        variant: 'destructive'
      })
    }
  }

  if (isLoading) return <div>Loading...</div>
  if (!timesheet) return <div>Timesheet not found</div>

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Timesheet Approval</h1>
        <TimesheetStatusIndicator status={timesheet.status} size="lg" />
      </div>

      <TimesheetDetails timesheet={timesheet} />

      {timesheet.status === 'pending_client_approval' && (
        <div className="flex justify-end">
          <Button onClick={() => setShowSignatureModal(true)}>
            Approve Timesheet
          </Button>
        </div>
      )}

      <SignatureCaptureModal 
        isOpen={showSignatureModal}
        onClose={() => setShowSignatureModal(false)}
        onSignatureSubmit={handleSignatureSubmit}
        title="Client Approval Signature"
        description="Please sign below to approve this timesheet"
      />
    </div>
  )
}

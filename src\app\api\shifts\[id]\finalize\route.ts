import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/middleware';
import { prisma } from '@/lib/prisma';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Only crew chiefs and managers can finalize shifts
    if (!['Crew Chief', 'Admin'].includes(user.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    const { id: shiftId } = await params;

    // Check if shift exists
    const shiftResult = await prisma.shift.findUnique({
      where: { id: shiftId },
      select: { id: true, status: true },
    });

    if (!shiftResult) {
      return NextResponse.json(
        { error: 'Shift not found' },
        { status: 404 }
      );
    }

    const shift = shiftResult;

    // Check if timesheet already exists
    const existingTimesheetResult = await prisma.timesheet.findUnique({
      where: { shiftId },
      select: { id: true },
    });

    let timesheetId;

    if (existingTimesheetResult) {
      timesheetId = existingTimesheetResult.id;
      await prisma.timesheet.update({
        where: { id: timesheetId },
        data: {
          status: 'Pending',
          // submitted_by: user.id,
          // submitted_at: new Date(),
        },
      });
    } else {
      const newTimesheetResult = await prisma.timesheet.create({
        data: {
          shiftId,
          status: 'Pending',
          // submitted_by: user.id,
          // submitted_at: new Date(),
        },
        select: { id: true },
      });
      timesheetId = newTimesheetResult.id;
    }

    // Update shift status to Completed (timesheet handles approval workflow)
    await prisma.shift.update({
      where: { id: shiftId },
      data: { status: 'Completed' },
    });

    return NextResponse.json({
      success: true,
      message: 'Shift finalized and sent for client approval',
      timesheetId,
    });
  } catch (error) {
    console.error('Error finalizing shift:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

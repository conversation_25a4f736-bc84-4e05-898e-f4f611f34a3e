import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getCurrentUser } from '@/lib/middleware';
import { UserRole } from '@prisma/client';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    if (![UserRole.Admin, UserRole.CrewChief].includes(user.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    const { id: shiftId } = await params;
    const body = await request.json();
    const { workerId } = body;

      if (!workerId) {
        return NextResponse.json(
          { error: 'Worker ID is required' },
          { status: 400 }
      );
    }

    // Get the assigned personnel record
    const assignedPersonnelResult = await prisma.assignedPersonnel.findUnique({
      where: { id: workerId },
      select: { id: true, userId: true, shiftId: true },
    });

    if (!assignedPersonnelResult) {
      return NextResponse.json(
        { error: 'Worker not found on this shift' },
        { status: 404 }
      );
    }

    const assignedPersonnel = assignedPersonnelResult;

    const existingEntries = await prisma.timeEntry.findMany({
      where: { assignedPersonnelId: workerId },
      orderBy: { entryNumber: 'asc' },
      select: { entryNumber: true },
    });

    let nextEntryNumber = 1;
    const existingEntryNumbers = existingEntries.map(e => e.entryNumber);
    for (let i = 1; i <= 3; i++) {
      if (!existingEntryNumbers.includes(i)) {
        nextEntryNumber = i;
        break;
      }
    }

    // Check if there's already an active entry
    const activeEntryResult = await prisma.timeEntry.findFirst({
      where: {
        assignedPersonnelId: workerId,
        isActive: true,
      },
    });

    if (activeEntryResult) {
      return NextResponse.json(
        { error: 'Employee is already clocked in' },
        { status: 400 }
      );
    }

    // Create new time entry with clock in time
    const now = new Date().toISOString();
    await prisma.timeEntry.create({
      data: {
        assignedPersonnelId: workerId,
        entryNumber: nextEntryNumber,
        clockIn: now,
        isActive: true,
      },
    });
    return NextResponse.json({
      success: true,
      message: 'Employee clocked in successfully',
    });
  } catch (error) {
    console.error('Error clocking in employee:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

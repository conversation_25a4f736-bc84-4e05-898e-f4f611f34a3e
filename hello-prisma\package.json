{"name": "prisma-typescript-starter", "version": "1.0.0", "main": "index.js", "license": "MIT", "scripts": {"build": "tsc", "start": "tsx src/index.ts", "dev": "tsc --watch"}, "devDependencies": {"@types/node": "22.15.32", "nodemon": "3.1.10", "prisma": "6.9.0", "tsx": "4.20.3", "typescript": "5.8.2"}, "dependencies": {"@prisma/client": "6.9.0", "@prisma/extension-accelerate": "^2.0.2"}, "prettier": {"trailingComma": "all", "tabWidth": 2, "printWidth": 80, "semi": true, "singleQuote": true, "jsxSingleQuote": false, "jsxBracketSameLine": false, "bracketSpacing": true, "arrowParens": "always"}}
# Quick deployment script for HoliTime (PowerShell)
# This script builds, pushes, and deploys the application

$ErrorActionPreference = "Stop"

$PROJECT_ID = "elated-fabric-460119-t3"
$REGION = "us-west2"
$SERVICE_NAME = "holitime"
$IMAGE_NAME = "gcr.io/$PROJECT_ID/${SERVICE_NAME}:latest"

Write-Host "🚀 Building and deploying HoliTime..." -ForegroundColor Blue

# Build the Docker image
Write-Host "📦 Building Docker image..." -ForegroundColor Yellow
docker build -t $IMAGE_NAME .

# Push to Google Container Registry
Write-Host "⬆️ Pushing to Container Registry..." -ForegroundColor Yellow
docker push $IMAGE_NAME

# Deploy to Cloud Run
Write-Host "🌐 Deploying to Cloud Run..." -ForegroundColor Yellow
gcloud run deploy $SERVICE_NAME `
    --image $IMAGE_NAME `
    --platform managed `
    --region $REGION `
    --allow-unauthenticated `
    --port 3000 `
    --memory 2Gi `
    --cpu 2 `
    --max-instances 5 `
    --timeout 300 `
    --set-env-vars NODE_ENV=production,PORT=3000,NEXTAUTH_URL=https://holitime-438323004618.us-west2.run.app,GOOGLE_CLIENT_ID=438323004618-7351haftdo2dm9s8vgdo2gsaqfuj9h9i.apps.googleusercontent.com,GOOGLE_CLIENT_SECRET=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 `
    --set-secrets "DATABASE_URL=DATABASE_URL:latest,NEXTAUTH_SECRET=NEXTAUTH_SECRET:latest,JWT_SECRET=JWT_SECRET:latest,SMTP_HOST=SMTP_HOST:latest,SMTP_PORT=SMTP_PORT:latest,SMTP_USER=SMTP_USER:latest,SMTP_PASS=SMTP_PASS:latest"

Write-Host "✅ Deployment completed!" -ForegroundColor Green
Write-Host "🌐 Service URL: https://holitime-438323004618.us-west2.run.app" -ForegroundColor Cyan
Write-Host "📊 Monitor logs: gcloud logs tail --service=$SERVICE_NAME --region=$REGION" -ForegroundColor Gray

#!/usr/bin/env node

/**
 * Environment Configuration Checker
 * 
 * This script validates the environment configuration and provides
 * helpful feedback about missing or misconfigured variables.
 */

const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

function printHeader(title) {
  console.log('\n' + colorize('='.repeat(60), 'cyan'));
  console.log(colorize(title.toUpperCase(), 'bright'));
  console.log(colorize('='.repeat(60), 'cyan'));
}

function printSection(title) {
  console.log('\n' + colorize(title, 'blue'));
  console.log(colorize('-'.repeat(title.length), 'blue'));
}

function printSuccess(message) {
  console.log(colorize('✓ ', 'green') + message);
}

function printWarning(message) {
  console.log(colorize('⚠ ', 'yellow') + message);
}

function printError(message) {
  console.log(colorize('✗ ', 'red') + message);
}

function printInfo(message) {
  console.log(colorize('ℹ ', 'blue') + message);
}

// Load environment variables
require('dotenv').config();

// Required environment variables
const requiredVars = {
  'NODE_ENV': {
    description: 'Application environment',
    validValues: ['development', 'production', 'test'],
    default: 'development'
  },
  'DATABASE_URL': {
    description: 'Database connection string',
    validator: (value) => value.startsWith('postgresql://') || value.startsWith('postgres://'),
    errorMessage: 'Must be a valid PostgreSQL connection string'
  },
  'NEXTAUTH_SECRET': {
    description: 'NextAuth.js secret key',
    validator: (value) => value.length >= 32,
    errorMessage: 'Must be at least 32 characters long'
  },
  'NEXTAUTH_URL': {
    description: 'Application URL',
    validator: (value) => value.startsWith('http://') || value.startsWith('https://'),
    errorMessage: 'Must be a valid URL starting with http:// or https://'
  },
  'GOOGLE_CLIENT_ID': {
    description: 'Google OAuth client ID',
    validator: (value) => value.endsWith('.apps.googleusercontent.com'),
    errorMessage: 'Must be a valid Google OAuth client ID'
  },
  'GOOGLE_CLIENT_SECRET': {
    description: 'Google OAuth client secret',
    validator: (value) => value.startsWith('GOCSPX-'),
    errorMessage: 'Must be a valid Google OAuth client secret'
  },
  'JWT_SECRET': {
    description: 'JWT secret key',
    validator: (value) => value.length >= 32,
    errorMessage: 'Must be at least 32 characters long'
  }
};

// Optional environment variables
const optionalVars = {
  'GOOGLE_API_KEY': 'Google API key for Sheets/Drive integration',
  'GOOGLE_AI_API_KEY': 'Google AI API key for Gemini integration',
  'SMTP_HOST': 'SMTP server hostname',
  'SMTP_PORT': 'SMTP server port',
  'SMTP_USER': 'SMTP username',
  'SMTP_PASS': 'SMTP password',
  'NEXT_PUBLIC_WS_URL': 'WebSocket URL for real-time features',
  'AWS_ACCESS_KEY_ID': 'AWS access key for S3 storage',
  'AWS_SECRET_ACCESS_KEY': 'AWS secret key for S3 storage',
  'AWS_REGION': 'AWS region for S3 storage',
  'AWS_S3_BUCKET': 'AWS S3 bucket name',
  'SENTRY_DSN': 'Sentry DSN for error tracking',
  'NEXT_PUBLIC_GA_ID': 'Google Analytics ID'
};

function checkRequiredVariables() {
  printSection('Required Environment Variables');
  
  let allValid = true;
  
  for (const [varName, config] of Object.entries(requiredVars)) {
    const value = process.env[varName];
    
    if (!value) {
      printError(`${varName} is missing`);
      printInfo(`  Description: ${config.description}`);
      if (config.default) {
        printInfo(`  Default: ${config.default}`);
      }
      allValid = false;
      continue;
    }
    
    if (config.validValues && !config.validValues.includes(value)) {
      printError(`${varName} has invalid value: ${value}`);
      printInfo(`  Valid values: ${config.validValues.join(', ')}`);
      allValid = false;
      continue;
    }
    
    if (config.validator && !config.validator(value)) {
      printError(`${varName} is invalid`);
      printInfo(`  ${config.errorMessage}`);
      allValid = false;
      continue;
    }
    
    printSuccess(`${varName} is configured`);
  }
  
  return allValid;
}

function checkOptionalVariables() {
  printSection('Optional Environment Variables');
  
  const configured = [];
  const missing = [];
  
  for (const [varName, description] of Object.entries(optionalVars)) {
    if (process.env[varName]) {
      configured.push({ name: varName, description });
    } else {
      missing.push({ name: varName, description });
    }
  }
  
  if (configured.length > 0) {
    console.log('\n' + colorize('Configured:', 'green'));
    configured.forEach(({ name, description }) => {
      printSuccess(`${name} - ${description}`);
    });
  }
  
  if (missing.length > 0) {
    console.log('\n' + colorize('Not configured (optional):', 'yellow'));
    missing.forEach(({ name, description }) => {
      printWarning(`${name} - ${description}`);
    });
  }
}

function checkEnvironmentSpecific() {
  printSection('Environment-Specific Checks');
  
  const nodeEnv = process.env.NODE_ENV || 'development';
  const nextAuthUrl = process.env.NEXTAUTH_URL;
  const databaseUrl = process.env.DATABASE_URL;
  
  printInfo(`Current environment: ${nodeEnv}`);
  
  if (nodeEnv === 'production') {
    if (nextAuthUrl && !nextAuthUrl.startsWith('https://')) {
      printWarning('NEXTAUTH_URL should use HTTPS in production');
    } else {
      printSuccess('NEXTAUTH_URL uses HTTPS (production)');
    }
    
    if (databaseUrl && !databaseUrl.includes('sslmode=require')) {
      printWarning('DATABASE_URL should use SSL in production');
    } else if (databaseUrl) {
      printSuccess('DATABASE_URL uses SSL (production)');
    }
    
    if (process.env.NEXTAUTH_SECRET === process.env.JWT_SECRET) {
      printWarning('NEXTAUTH_SECRET and JWT_SECRET should be different in production');
    } else {
      printSuccess('NEXTAUTH_SECRET and JWT_SECRET are different');
    }
  }
  
  // Check email configuration completeness
  const emailVars = ['SMTP_HOST', 'SMTP_PORT', 'SMTP_USER', 'SMTP_PASS'];
  const emailConfigured = emailVars.map(v => !!process.env[v]);
  const hasAnyEmail = emailConfigured.some(v => v);
  const hasAllEmail = emailConfigured.every(v => v);
  
  if (hasAnyEmail && !hasAllEmail) {
    printWarning('Incomplete email configuration - all SMTP variables should be set');
    emailVars.forEach((varName, index) => {
      if (!emailConfigured[index]) {
        printError(`  Missing: ${varName}`);
      }
    });
  } else if (hasAllEmail) {
    printSuccess('Email configuration is complete');
  } else {
    printInfo('Email configuration not set (optional)');
  }
}

function checkFileExists(filePath, description) {
  if (fs.existsSync(filePath)) {
    printSuccess(`${description} exists: ${filePath}`);
    return true;
  } else {
    printWarning(`${description} not found: ${filePath}`);
    return false;
  }
}

function checkFiles() {
  printSection('Configuration Files');
  
  const rootDir = path.resolve(__dirname, '..');
  
  checkFileExists(path.join(rootDir, '.env'), '.env file');
  checkFileExists(path.join(rootDir, '.env.example'), '.env.example template');
  checkFileExists(path.join(rootDir, 'prisma/schema.prisma'), 'Prisma schema');
  checkFileExists(path.join(rootDir, 'next.config.mjs'), 'Next.js config');
}

function generateSecrets() {
  printSection('Secret Generation Helper');
  
  const crypto = require('crypto');
  
  printInfo('You can use these generated secrets:');
  console.log('');
  console.log(colorize('NEXTAUTH_SECRET=', 'cyan') + crypto.randomBytes(32).toString('base64'));
  console.log(colorize('JWT_SECRET=', 'cyan') + crypto.randomBytes(32).toString('base64'));
  console.log('');
  printInfo('Or generate them manually with: openssl rand -base64 32');
}

function printSummary(requiredValid) {
  printSection('Summary');
  
  if (requiredValid) {
    printSuccess('All required environment variables are configured correctly!');
    printInfo('Your application should start without environment-related errors.');
  } else {
    printError('Some required environment variables are missing or invalid.');
    printInfo('Please fix the issues above before starting the application.');
  }
  
  console.log('\n' + colorize('Next steps:', 'bright'));
  console.log('1. Copy .env.example to .env if you haven\'t already');
  console.log('2. Fill in the missing values in your .env file');
  console.log('3. Run this script again to verify your configuration');
  console.log('4. Start your application with: npm run dev');
  
  console.log('\n' + colorize('For more help:', 'bright'));
  console.log('- Read docs/ENVIRONMENT_CONFIGURATION.md');
  console.log('- Check the .env.example file for examples');
  console.log('- Visit /api/debug/environment (development only) for runtime checks');
}

function main() {
  printHeader('HoliTime Environment Configuration Checker');
  
  const requiredValid = checkRequiredVariables();
  checkOptionalVariables();
  checkEnvironmentSpecific();
  checkFiles();
  
  if (!requiredValid) {
    generateSecrets();
  }
  
  printSummary(requiredValid);
  
  process.exit(requiredValid ? 0 : 1);
}

// Run the checker
main();

# Project Blueprint: Hands On Labor

## 1. High-Level Overview
- **Application Purpose:** A comprehensive workforce management solution designed for companies to manage employees, clients, jobs, and shifts. It facilitates scheduling, time tracking, and administrative oversight. The target users are company administrators, crew chiefs, and employees.
- **Core Functionality:**
    1.  **User & Company Management:** Admins can manage users, companies, and their relationships.
    2.  **Job & Shift Scheduling:** Create and manage jobs for clients, and schedule shifts with specific worker requirements.
    3.  **Time Tracking & Timesheets:** Employees can clock in/out of shifts, and timesheets are generated for approval.
    4.  **Role-Based Access Control:** Different user roles (<PERSON><PERSON>, Crew<PERSON>hief, Employee) have distinct permissions and dashboard views.
    5.  **Data Import/Export:** Functionality to import data via CSV and interact with Google Drive/Sheets.
- **Architectural Pattern:** The application follows a **Monolithic Client-Server** architecture. The frontend is built with Next.js (React) and the backend is a set of API routes within the same Next.js application, connecting to a PostgreSQL database via the Prisma ORM. This is a common and effective pattern for full-stack Next.js applications, as it simplifies development and deployment by keeping the entire codebase in a single repository.

## 2. Technology Stack & Dependencies
- **`package.json`:**
```json
{
  "name": "nextn",
  "version": "0.1.0",
  "private": true,
  "scripts": {
    "dev": "next dev",
    "genkit:dev": "genkit start -- tsx src/ai/dev.ts",
    "genkit:watch": "genkit start -- tsx --watch src/ai/dev.ts",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "typecheck": "npx tsc --noEmit",
    "migrate": "npx prisma migrate dev --schema=./prisma/schema.prisma",
    "migrate:prod": "npx prisma migrate deploy --schema=./prisma/schema.prisma",
    "db:setup": "npm run migrate",
    "deploy": "powershell -ExecutionPolicy Bypass -File handsonlabor-website/deploy-cloud-run.ps1 -ProjectId holitime-465520",
    "deploy:bash": "./deploy.sh",
    "studio": "npx prisma studio",
    "fix-jobs": "cross-env NODE_TLS_REJECT_UNAUTHORIZED=0 tsx scripts/fix-jobs.ts",
    "import-new-shifts": "tsx scripts/import-new-shifts.ts",
    "db:seed": "npx prisma db seed"
  },
  "prisma": {
    "seed": "tsx prisma/seed.ts"
  },
  "dependencies": {
    "@auth/pg-adapter": "^1.10.0",
    "@faker-js/faker": "^9.9.0",
    "@genkit-ai/googleai": "^1.14.0",
    "@genkit-ai/next": "^1.14.0",
    "@google-cloud/cloud-sql-connector": "^1.8.2",
    "@google/generative-ai": "^0.11.0",
    "@hookform/resolvers": "^5.1.1",
    "@mantine/core": "^8.1.3",
    "@mantine/form": "^8.1.3",
    "@mantine/hooks": "^8.1.3",
    "@mantine/notifications": "^8.1.3",
    "@prisma/client": "^6.12.0",
    "@radix-ui/react-accordion": "^1.2.11",
    "@radix-ui/react-alert-dialog": "^1.1.14",
    "@radix-ui/react-avatar": "^1.1.10",
    "@radix-ui/react-checkbox": "^1.3.2",
    "@radix-ui/react-dialog": "^1.1.14",
    "@radix-ui/react-dropdown-menu": "^2.1.15",
    "@radix-ui/react-label": "^2.1.7",
    "@radix-ui/react-menubar": "^1.1.15",
    "@radix-ui/react-popover": "^1.1.14",
    "@radix-ui/react-progress": "^1.1.7",
    "@radix-ui/react-radio-group": "^1.3.7",
    "@radix-ui/react-scroll-area": "^1.2.9",
    "@radix-ui/react-select": "^2.2.5",
    "@radix-ui/react-separator": "^1.1.7",
    "@radix-ui/react-slider": "^1.3.5",
    "@radix-ui/react-slot": "^1.2.3",
    "@radix-ui/react-switch": "^1.2.5",
    "@radix-ui/react-tabs": "^1.1.12",
    "@radix-ui/react-toast": "^1.2.14",
    "@radix-ui/react-tooltip": "^1.2.7",
    "@react-native-community/push-notification-ios": "^1.11.0",
    "@tanstack/react-query": "^5.83.0",
    "@types/bcryptjs": "^3.0.0",
    "@types/jsonwebtoken": "^9.0.10",
    "@types/pg": "^8.15.4",
    "bcryptjs": "^3.0.2",
    "class-variance-authority": "^0.7.1",
    "clsx": "^2.1.1",
    "date-fns": "^4.1.0",
    "dotenv": "^17.2.0",
    "dotenv-cli": "^8.0.0",
    "eslint": "^8.0.0",
    "firebase": "^11.10.0",
    "genkit": "^1.14.0",
    "go": "^3.0.1",
    "googleapis": "^152.0.0",
    "jsonwebtoken": "^9.0.2",
    "jspdf": "^3.0.1",
    "jspdf-autotable": "^5.0.2",
    "lucide-react": "^0.525.0",
    "next": "14.2.5",
    "next-auth": "^4.24.7",
    "patch-package": "^8.0.0",
    "pdf-lib": "^1.17.1",
    "pg": "^8.16.3",
    "postcss-preset-mantine": "^1.18.0",
    "psql": "^0.0.1",
    "react": "^18.3.1",
    "react-day-picker": "^9.8.0",
    "react-dom": "^18.3.1",
    "react-hook-form": "^7.60.0",
    "react-pdf": "^10.0.1",
    "react-signature-canvas": "^1.1.0-alpha.2",
    "react-toastify": "^11.0.5",
    "recharts": "^3.1.0",
    "sharp": "^0.34.3",
    "tailwind-merge": "^3.3.1",
    "xlsx": "^0.18.5",
    "zod": "^3.25.76",
    "zustand": "^5.0.6"
  },
  "devDependencies": {
    "@types/csv-parse": "^1.1.12",
    "@types/node": "^20.11.0",
    "@types/react": "18.3.3",
    "@types/react-dom": "^18.3.0",
    "@types/react-native": "^0.72.8",
    "@types/react-native-push-notification": "^8.1.4",
    "@types/react-signature-canvas": "^1.0.7",
    "@types/tailwindcss": "^3.0.11",
    "@typescript-eslint/eslint-plugin": "^8.37.0",
    "cross-env": "^7.0.3",
    "csv-parse": "^6.0.0",
    "eslint-config-next": "^14.2.5",
    "genkit-cli": "^1.14.0",
    "postcss": "^8.4.30",
    "prisma": "^6.12.0",
    "tailwindcss": "^4.1.11",
    "tsx": "^4.20.3",
    "typescript": "^5.8.3"
  }
}
```
- **Rationale:**
    - **Next.js:** Chosen as a full-stack framework to unify frontend and backend development, enabling server-side rendering for performance and SEO.
    - **Prisma:** Selected as the ORM for its type-safety and declarative schema, which simplifies database interactions and migrations.
    - **PostgreSQL:** A robust and reliable relational database suitable for managing the structured data of users, jobs, and shifts.
    - **Auth.js (NextAuth):** A standard for authentication in Next.js, providing built-in support for credentials and OAuth providers like Google.
    - **Tailwind CSS / Radix UI / Mantine:** A combination for building the UI. Tailwind provides utility-first styling, Radix offers unstyled, accessible components, and Mantine provides a higher-level component library.

## 3. Directory & File Structure
```
/
├── .dockerignore
├── .env
├── .env.development
├── .eslintrc.json
├── .gitignore
├── .npmrc
├── 1.env
├── 1.env.production
├── certs/
├── cloudbuild.yaml
├── components.json
├── deploy-cloud-run.sh
├── docker-compose.yml
├── Dockerfile
├── hello-prisma/
│   ├── .gitignore
│   ├── package.json
│   ├── README.md
│   ├── tsconfig.json
│   ├── prisma/
│   │   └── schema.prisma
│   └── src/
│       └── index.ts
├── next.config.mjs
├── nodesource_setup.sh
├── package-lock.json
├── package.json
├── postcss.config.mjs
├── prisma/
│   ├── migrations/
│   │   ├── 20250717012133_initial_migration/
│   │   │   └── migration.sql
│   │   ├── 20250717111954_add_user_fields/
│   │   │   └── migration.sql
│   │   ├── 20250717112521_add_time_entry_model/
│   │   │   └── migration.sql
│   │   └── migration_lock.toml
│   ├── schema.prisma
│   └── seed.ts
├── public/
│   └── favicon.ico
├── scripts/
│   ├── fix-jobs.ts
│   ├── import-new-shifts.ts
│   ├── prisma-clean.js
│   ├── reset-password.ts
│   ├── test_import.ts
│   ├── test_shifts.csv
│   ├── test-db.js
│   ├── test-net.js
│   ├── test-prisma.js
│   └── verify_import.ts
├── settings.json
├── src/
│   ├── app/
│   │   ├── (app)/
│   │   │   ├── (dashboards)/
│   │   │   │   ├── client/
│   │   │   │   │   └── page.tsx
│   │   │   │   ├── crew-chief/
│   │   │   │   │   └── page.tsx
│   │   │   │   ├── employee/
│   │   │   │   │   └── page.tsx
│   │   │   │   └── manager/
│   │   │   │       └── page.tsx
│   │   │   ├── admin/
│   │   │   │   ├── clients/
│   │   │   │   │   ├── import/
│   │   │   │   │   │   └── page.tsx
│   │   │   │   │   ├── new/
│   │   │   │   │   │   └── page.tsx
│   │   │   │   │   └── page.tsx
│   │   │   │   ├── employees/
│   │   │   │   │   ├── new/
│   │   │   │   │   │   └── page.tsx
│   │   │   │   │   ├── reports/
│   │   │   │   │   │   └── page.tsx
│   │   │   │   │   └── page.tsx
│   │   │   │   ├── jobs/
│   │   │   │   │   ├── new/
│   │   │   │   │   │   └── page.tsx
│   │   │   │   │   ├── templates/
│   │   │   │   │   │   └── page.tsx
│   │   │   │   │   └── page.tsx
│   │   │   │   ├── merge/
│   │   │   │   │   └── page.tsx
│   │   │   │   ├── page.tsx
│   │   │   │   ├── settings/
│   │   │   │   │   └── page.tsx
│   │   │   │   ├── shifts/
│   │   │   │   │   ├── new/
│   │   │   │   │   │   └── page.tsx
│   │   │   │   │   ├── templates/
│   │   │   │   │   │   └── page.tsx
│   │   │   │   │   └── page.tsx
│   │   │   │   ├── timesheets/
│   │   │   │   │   └── page.tsx
│   │   │   │   └── users/
│   │   │   │       └── page.tsx
│   │   │   ├── clients/
│   │   │   │   ├── [id]/
│   │   │   │   │   └── page.tsx
│   │   │   │   ├── new/
│   │   │   │   │   └── page.tsx
│   │   │   │   └── page.tsx
│   │   │   ├── dashboard/
│   │   │   │   └── page.tsx
│   │   │   ├── documents/
│   │   │   │   ├── [id]/
│   │   │   │   │   └── page.tsx
│   │   │   │   └── page.tsx
│   │   │   ├── employees/
│   │   │   │   └── page.tsx
│   │   │   ├── jobs/
│   │   │   │   ├── [id]/
│   │   │   │   │   ├── edit/
│   │   │   │   │   │   └── page.tsx
│   │   │   │   │   ├── shifts/
│   │   │   │   │   │   └── new/
│   │   │   │   │   │       └── page.tsx
│   │   │   │   │   └── page.tsx
│   │   │   │   ├── new/
│   │   │   │   │   └── page.tsx
│   │   │   │   └── page.tsx
│   │   │   ├── layout.tsx
│   │   │   ├── shifts/
│   │   │   │   ├── [id]/
│   │   │   │   │   ├── edit/
│   │   │   │   │   │   └── page.tsx
│   │   │   │   │   └── page.tsx
│   │   │   │   └── page.tsx
│   │   │   ├── staffing/
│   │   │   │   └── page.tsx
│   │   │   └── timesheets/
│   │   │       ├── [id]/
│   │   │       │   ├── approve/
│   │   │       │   │   └── page.tsx
│   │   │       │   ├── client-review/
│   │   │       │   │   └── page.tsx
│   │   │       │   ├── manager-approval/
│   │   │       │   │   └── page.tsx
│   │   │       │   ├── manager-review/
│   │   │       │   │   └── page.tsx
│   │   │       │   ├── page.tsx
│   │   │       │   └── review/
│   │   │       │       └── page.tsx
│   │   │       ├── client-approval/
│   │   │       │   ├── [id]/
│   │   │       │   │   └── page.tsx
│   │   │       │   └── route.ts
│   │   │       ├── manager-approval/
│   │   │       │   ├── [id]/
│   │   │       │   │   └── page.tsx
│   │   │       │   └── route.ts
│   │   │       ├── page.tsx
│   │   │       └── pending/
│   │   │           └── page.tsx
│   │   ├── admin/
│   │   │   └── crew-chief-permissions/
│   │   │       └── page.tsx
│   │   ├── api/
│   │   │   ├── admin/
│   │   │   │   ├── merge/
│   │   │   │   │   └── route.ts
│   │   │   │   └── migrate/
│   │   │   │       └── route.ts
│   │   │   ├── announcements/
│   │   │   │   └── route.ts
│   │   │   ├── auth/
│   │   │   │   ├── [...nextauth]/
│   │   │   │   │   └── route.ts
│   │   │   │   ├── forgot-password/
│   │   │   │   │   └── route.ts
│   │   │   │   ├── login/
│   │   │   │   ├── logout/
│   │   │   │   │   └── route.ts
│   │   │   │   ├── me/
│   │   │   │   │   └── route.ts
│   │   │   │   ├── register/
│   │   │   │   │   └── route.ts
│   │   │   │   ├── reset-password/
│   │   │   │   │   └── route.ts
│   │   │   │   └── route.ts
│   │   │   ├── cascade-delete/
│   │   │   │   ├── client/
│   │   │   │   │   └── [id]/
│   │   │   │   │       └── route.ts
│   │   │   │   ├── job/
│   │   │   │   │   └── [id]/
│   │   │   │   │       └── route.ts
│   │   │   │   └── shift/
│   │   │   │       └── [id]/
│   │   │   │           └── route.ts
│   │   │   ├── clients/
│   │   │   │   ├── [id]/
│   │   │   │   │   ├── dashboard/
│   │   │   │   │   │   └── route.ts
│   │   │   │   │   ├── jobs/
│   │   │   │   │   │   └── route.ts
│   │   │   │   │   └── route.ts
│   │   │   │   ├── by-slug/
│   │   │   │   │   └── route.ts
│   │   │   │   ├── count/
│   │   │   │   ├── recent/
│   │   │   │   └── route.ts
│   │   │   ├── crew-chief/
│   │   │   │   ├── [id]/
│   │   │   │   │   └── dashboard/
│   │   │   │   │       └── route.ts
│   │   │   ├── crew-chief-permissions/
│   │   │   │   ├── check/
│   │   │   │   │   └── route.ts
│   │   │   │   └── manage/
│   │   │   │       └── route.ts
│   │   │   ├── debug/
│   │   │   │   ├── db-test/
│   │   │   │   │   └── route.ts
│   │   │   │   ├── environment/
│   │   │   │   │   └── route.ts
│   │   │   │   ├── google-api-test/
│   │   │   │   │   └── route.ts
│   │   │   │   ├── google-drive-detailed/
│   │   │   │   │   └── route.ts
│   │   │   │   ├── google-sheets-test/
│   │   │   │   │   └── route.ts
│   │   │   │   ├── nextauth/
│   │   │   │   │   └── route.ts
│   │   │   │   ├── shifts/
│   │   │   │   │   └── route.ts
│   │   │   │   ├── time-entries-schema/
│   │   │   │   │   └── route.ts
│   │   │   │   └── token-info/
│   │   │   │       └── route.ts
│   │   │   ├── employees/
│   │   │   │   ├── [id]/
│   │   │   │   │   └── dashboard/
│   │   │   │   │       └── route.ts
│   │   │   │   └── route.ts
│   │   │   ├── health/
│   │   │   │   └── route.ts
│   │   │   ├── import/
│   │   │   │   ├── csv/
│   │   │   │   │   ├── import/
│   │   │   │   │   │   └── route.ts
│   │   │   │   │   ├── parse/
│   │   │   │   │   │   └── route.ts
│   │   │   │   │   └── template/
│   │   │   │   │       └── route.ts
│   │   │   │   ├── google-drive/
│   │   │   │   │   ├── auth/
│   │   │   │   │   │   └── route.ts
│   │   │   │   │   ├── callback/
│   │   │   │   │   │   └── route.ts
│   │   │   │   │   ├── extract/
│   │   │   │   │   │   └── route.ts
│   │   │   │   │   ├── files/
│   │   │   │   │   │   └── route.ts
│   │   │   │   │   └── import/
│   │   │   │   │       └── route.ts
│   │   │   │   └── google-sheets/
│   │   │   │       ├── fetch/
│   │   │   │       │   └── [id]/
│   │   │   │       │       └── route.ts
│   │   │   │       ├── fetch-with-oauth/
│   │   │   │       │   └── [id]/
│   │   │   │       │       └── route.ts
│   │   │   │       └── gemini/
│   │   │   │           └── route.ts
│   │   │   ├── jobs/
│   │   │   │   ├── [id]/
│   │   │   │   │   ├── shifts/
│   │   │   │   │   │   └── route.ts
│   │   │   │   │   └── route.ts
│   │   │   │   ├── count/
│   │   │   │   ├── recent/
│   │   │   │   │   └── route.ts
│   │   │   │   └── route.ts
│   │   │   ├── notifications/
│   │   │   │   ├── [id]/
│   │   │   │   │   └── read/
│   │   │   │   │       └── route.ts
│   │   │   │   ├── mark-all-read/
│   │   │   │   │   └── route.ts
│   │   │   │   └── route.ts
│   │   │   ├── shifts/
│   │   │   │   ├── [id]/
│   │   │   │   │   ├── assign/
│   │   │   │   │   │   └── route.ts
│   │   │   │   │   ├── assign-worker/
│   │   │   │   │   │   └── route.ts
│   │   │   │   │   ├── assigned/
│   │   │   │   │   │   ├── [assignmentId]/
│   │   │   │   │   │   │   ├── clock/
│   │   │   │   │   │   │   │   └── route.ts
│   │   │   │   │   │   │   ├── end-shift/
│   │   │   │   │   │   │   │   └── route.ts
│   │   │   │   │   │   │   └── route.ts
│   │   │   │   │   │   └── route.ts
│   │   │   │   │   ├── check-conflicts/
│   │   │   │   │   │   └── route.ts
│   │   │   │   │   ├── clock-in/
│   │   │   │   │   │   └── route.ts
│   │   │   │   │   ├── clock-out/
│   │   │   │   │   │   └── route.ts
│   │   │   │   │   ├── details/
│   │   │   │   │   │   └── route.ts
│   │   │   │   │   ├── end-all-shifts/
│   │   │   │   │   │   └── route.ts
│   │   │   │   │   ├── end-shift/
│   │   │   │   │   │   └── route.ts
│   │   │   │   │   ├── finalize/
│   │   │   │   │   │   └── route.ts
│   │   │   │   │   ├── finalize-timesheet/
│   │   │   │   │   │   └── route.ts
│   │   │   │   │   ├── finalize-timesheet-simple/
│   │   │   │   │   │   └── route.ts
│   │   │   │   │   ├── route.ts
│   │   │   │   │   └── worker-requirements/
│   │   │   │   │       └── route.ts
│   │   │   │   ├── by-date/
│   │   │   │   │   └── route.ts
│   │   │   │   ├── by-slug/
│   │   │   │   │   └── route.ts
│   │   │   │   ├── count/
│   │   │   │   ├── recent/
│   │   │   │   ├── route.ts
│   │   │   │   └── today/
│   │   │   │       └── route.ts
│   │   │   ├── time-entries/
│   │   │   ├── timesheets/
│   │   │   │   ├── [id]/
│   │   │   │   │   ├── approve/
│   │   │   │   │   │   └── route.ts
│   │   │   │   │   ├── finalize/
│   │   │   │   │   │   └── route.ts
│   │   │   │   │   ├── generate-pdf/
│   │   │   │   │   │   └── route.ts
│   │   │   │   │   ├── pdf/
│   │   │   │   │   │   └── route.ts
│   │   │   │   │   ├── review/
│   │   │   │   │   │   └── route.ts
│   │   │   │   │   └── route.ts
│   │   │   │   ├── pending/
│   │   │   │   │   └── route.ts
│   │   │   │   └── route.ts
│   │   │   └── users/
│   │   │       ├── [id]/
│   │   │       │   ├── reset-password/
│   │   │       │   │   └── route.ts
│   │   │       │   └── route.ts
│   │   │       └── route.ts
│   │   ├── globals.css
│   │   ├── google-drive-callback/
│   │   │   └── page.tsx
│   │   ├── import/
│   │   │   └── page.tsx
│   │   ├── layout.tsx
│   │   ├── login/
│   │   │   └── page.tsx
│   │   ├── page.tsx
│   │   ├── signup/
│   │   │   └── page.tsx
│   │   └── unauthorized/
│   │       └── page.tsx
│   ├── components/
│   │   ├── auth-guard.tsx
│   │   ├── bulk-shift-operations.tsx
│   │   ├── cascade-delete-dialog.tsx
│   │   ├── client-portal-landing.tsx
│   │   ├── crew-chief-permission-badge.tsx
│   │   ├── crew-chief-permission-manager.tsx
│   │   ├── csv-data-preview.tsx
│   │   ├── csv-import.tsx
│   │   ├── danger-zone.tsx
│   │   ├── dashboard/
│   │   │   └── shift-management/
│   │   │       ├── hooks.ts
│   │   │       ├── shift-actions.tsx
│   │   │       ├── shift-manager.tsx
│   │   │       ├── shift-stats.tsx
│   │   │       ├── types.ts
│   │   │       └── worker-card.tsx
│   │   ├── enhanced-notification-center.tsx
│   │   ├── error-boundary.tsx
│   │   ├── error-states.tsx
│   │   ├── google-drive-picker.tsx
│   │   ├── google-sheets-gemini-processor.tsx
│   │   ├── google-sheets-id-input.tsx
│   │   ├── Header.tsx
│   │   ├── import-page-client.tsx
│   │   ├── loading-states.tsx
│   │   ├── notification-center.tsx
│   │   ├── pdf-viewer.tsx
│   │   ├── providers/
│   │   │   ├── session-provider.tsx
│   │   │   └── theme-provider.tsx
│   │   ├── shift-time-management.tsx
│   │   ├── shifts-table-skeleton.tsx
│   │   ├── signature-capture-modal.tsx
│   │   ├── signature-pad.tsx
│   │   ├── timesheet-details.tsx
│   │   ├── timesheet-management.tsx
│   │   ├── timesheet-status-indicator.tsx
│   │   └── ui/
│   │       ├── accordion.tsx
│   │       ├── alert-dialog.tsx
│   │       ├── alert.tsx
│   │       ├── avatar.tsx
│   │       ├── badge.tsx
│   │       ├── button.tsx
│   │       ├── card.tsx
│   │       ├── checkbox.tsx
│   │       ├── dialog.tsx
│   │       ├── dropdown-menu.tsx
│   │       ├── input.tsx
│   │       ├── label.tsx
│   │       ├── popover.tsx
│   │       ├── progress.tsx
│   │       ├── scroll-area.tsx
│   │       ├── select.tsx
│   │       ├── separator.tsx
│   │       ├── skeleton.tsx
│   │       ├── table.tsx
│   │       ├── tabs.tsx
│   │       ├── textarea.tsx
│   │       └── tooltip.tsx
│   ├── hooks/
│   ├── lib/
│   │   ├── auth-api.ts
│   │   ├── auth-config.ts
│   │   ├── auth.ts
│   │   ├── google-drive.ts
│   │   ├── google-sheets.ts
│   │   ├── migrations/
│   │   ├── prisma.ts
│   │   ├── services/
│   │   ├── stores/
│   │   ├── types/
│   │   ├── utils/
│   │   └── withAuth.tsx
│   └── types/
│       └── next-auth.d.ts
├── vscode-next/
│   ├── images/
│   ├── src/
│   │   ├── helpers/
│   │   ├── plugins/
│   │   │   └── nextjs/
│   │   │       ├── tests/
│   │   │       │   ├── __fixtures__/
│   │   │       │   ├── __helpers__/
│   │   │       │   └── __snapshots__/
│   │   └── test/
│   │       └── suite/
└── (File list truncated)
```

## 4. Database Schema & Data Models
- **Database:** PostgreSQL
- **ORM:** Prisma
- **Schema Definition (`prisma/schema.prisma`):**
```prisma
// Complete and validated Prisma schema

datasource db {
  provider = "postgresql"
  url = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

enum UserRole {
  Staff
  Admin
  CompanyUser 
  CrewChief
  Employee
}

model User {
  id             String      @id @default(cuid())
  name           String
  email          String      @unique
  passwordHash   String
  role           UserRole    @default(Staff)
  avatarUrl      String?
  isActive       Boolean     @default(true)
  
  // Additional employee-specific fields
  crewChiefEligible     Boolean   @default(false)
  forkOperatorEligible  Boolean   @default(false)  
  certifications        String[]  @default([])
  performance           Float?
  location              String?
  
  // Relations
  companyId     String?
  company       Company?    @relation(fields: [companyId], references: [id])
  assignments   AssignedPersonnel[]

  @@map("users")
}

model Company {
  id        String    @id @default(cuid())
  name      String    @unique
  address   String?
  phone     String?
  email     String?
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt

  // Relations
  users     User[]
  jobs      Job[]

  @@map("companies")
}

model AssignedPersonnel {
  id            String      @id @default(cuid())
  shift         Shift       @relation(fields: [shiftId], references: [id])
  shiftId       String
  user          User        @relation(fields: [userId], references: [id])
  userId        String
  roleCode      String      @default("WR") // WR = Worker, CC = Crew Chief
  isPlaceholder Boolean     @default(false)
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt
  
  // Relations
  timeEntries TimeEntry[]
  permissions CrewChiefPermission[]

  @@map("assigned_personnel")
}

model TimeEntry {
  id                 String    @id @default(cuid())
  assignedPersonnel   AssignedPersonnel @relation(fields: [assignedPersonnelId], references: [id])
  assignedPersonnelId String
  clockIn            DateTime
  clockOut           DateTime?
  breakStart         DateTime?
  breakEnd           DateTime?
  notes              String?
  verified           Boolean   @default(false)
  createdAt          DateTime  @default(now())
  updatedAt          DateTime  @updatedAt

  @@map("time_entries")
}

model CrewChiefPermission {
  id            String      @id @default(cuid())
  permissionType String     // 'shift' or 'job'
  targetId      String      // shiftId or jobId
  assignedPersonnel AssignedPersonnel @relation(fields: [assignedPersonnelId], references: [id])
  assignedPersonnelId String
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt

  @@map("crew_chief_permissions")
}

model Shift {
  id                String      @id @default(cuid())
  job               Job         @relation(fields: [jobId], references: [id])
  jobId             String
  date              DateTime
  startTime         DateTime
  endTime           DateTime
  requestedWorkers  Int?
  status            String      @default("pending")
  createdAt         DateTime    @default(now())
  updatedAt         DateTime    @updatedAt

  // Relations
  assignedPersonnel AssignedPersonnel[]
  timesheets       Timesheet[]

  @@map("shifts")
}

model Job {
  id           String      @id @default(cuid())
  name         String
  description  String?
  isCompleted  Boolean     @default(false)
  company      Company     @relation(fields: [companyId], references: [id])
  companyId    String
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt

  // Relations
  shifts      Shift[]
  documents   Document[]

  @@map("jobs")
}

model Document {
  id           String      @id @default(cuid())
  name         String
  url          String
  job          Job?        @relation(fields: [jobId], references: [id])
  jobId        String?
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt

  @@map("documents")
}

model Timesheet {
  id            String      @id @default(cuid())
  shift         Shift       @relation(fields: [shiftId], references: [id])
  shiftId       String
  status        String      @default("pending")
  submittedAt   DateTime?
  approvedAt    DateTime?
  rejectedAt    DateTime?
  approvedById  String?
  notes         String?
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt

  @@map("timesheets")
}
```

## 5. API Endpoint Specification
*(This section would be populated by analyzing every `route.ts` file. Due to the large number of API routes, this is a representative sample. A full blueprint would detail every single one.)*

- **Endpoint:** `POST /api/auth/login`
  - **Description:** Authenticates a user with email and password.
  - **Authentication:** None required.
  - **Request Body:**
    ```json
    {
      "type": "object",
      "properties": {
        "email": { "type": "string", "format": "email" },
        "password": { "type": "string" }
      },
      "required": ["email", "password"]
    }
    ```
  - **Success Response (200 OK):** Sets an HTTP-only session cookie and redirects.
  - **Error Responses:**
    - **401 Unauthorized:** Invalid credentials.

- **Endpoint:** `GET /api/jobs`
  - **Description:** Retrieves a list of all jobs.
  - **Authentication:** Required (JWT session). Role: Admin.
  - **Success Response (200 OK):**
    ```json
    [
      {
        "id": "clx...",
        "name": "Job Name",
        "description": "Job description",
        "isCompleted": false,
        "companyId": "clx...",
        ...
      }
    ]
    ```
  - **Error Responses:**
    - **401 Unauthorized:** Not authenticated.
    - **403 Forbidden:** Not an Admin.

- **Endpoint:** `POST /api/shifts/[id]/clock-in`
  - **Description:** Clocks an assigned user into a specific shift.
  - **Authentication:** Required (JWT session).
  - **Path Parameters:**
    - `id`: (string) - The ID of the shift.
  - **Request Body:**
    ```json
    {
      "type": "object",
      "properties": {
        "userId": { "type": "string" }
      },
      "required": ["userId"]
    }
    ```
  - **Success Response (200 OK):**
    ```json
    { "message": "Clock-in successful" }
    ```
  - **Error Responses:**
    - **404 Not Found:** Shift or user not found.
    - **400 Bad Request:** User already clocked in.

## 6. Component-Level Deep Dive
*(This section requires a file-by-file analysis. The following are representative examples.)*

### 6.1. Backend Services / Modules

- **File Path:** `src/lib/auth.ts`
  - **Purpose:** Contains core authentication logic for user creation and credential verification.
  - **Dependencies:** `bcryptjs`, `@prisma/client`.
  - **Functions:**
    - `authenticateUser(email, password)`:
      1. Fetches a user from the database by email.
      2. If user exists, compares the provided password with the stored `passwordHash` using `bcrypt.compare`.
      3. Returns the user object if successful, otherwise null.
    - `getUserByEmail(email)`:
      1. Fetches and returns a user from the database by their email address.
    - `createUser(userData)`:
      1. Hashes the `password` from `userData` using `bcrypt.hash`.
      2. Creates a new user in the database with the provided data and hashed password.
      3. Returns the newly created user.

- **File Path:** `src/app/api/jobs/route.ts`
  - **Purpose:** Defines the API endpoints for creating and listing jobs.
  - **Dependencies:** `next/server`, `@/lib/prisma`, `next-auth`.
  - **Functions:**
    - `GET(req)`:
      1. Verifies the user session using `getServerSession`.
      2. Checks if the user role is 'Admin'. If not, returns a 403 Forbidden error.
      3. Fetches all jobs from the database using `prisma.job.findMany`.
      4. Returns a 200 OK response with the list of jobs.
    - `POST(req)`:
      1. Verifies the user session and admin role.
      2. Parses the request body for job data (`name`, `description`, `companyId`).
      3. Creates a new job in the database using `prisma.job.create`.
      4. Returns a 201 Created response with the new job object.

### 6.2. Frontend Components

- **File Path:** `src/app/(app)/admin/jobs/page.tsx`
  - **Purpose:** Displays a table of all jobs for administrators.
  - **Dependencies:** `react`, `@tanstack/react-query` (for data fetching), `@/components/ui/table`, `next/link`.
  - **State:** Manages the fetched job data, loading status, and error status via `useQuery`.
  - **Lifecycle / Effects:**
    - `useEffect`: Triggers a fetch to the `/api/jobs` endpoint on component mount using `useQuery`.
  - **Render Logic:**
    - If loading, displays a skeleton loader.
    - If error, displays an error message.
    - If data is available, renders a `<Table>` component, mapping over the jobs data to create a `<TableRow>` for each job, displaying its name, company, and status. Each row includes a link to the job's detail page.

- **File Path:** `src/components/auth-guard.tsx`
  - **Purpose:** A higher-order component (or wrapper) that protects routes based on user authentication status and role.
  - **Dependencies:** `react`, `next/router`, `next-auth/react`.
  - **Props:**
    - `children`: (React.ReactNode) - The component to render if authorized.
    - `allowedRoles`: (string[]) - An array of roles permitted to view the component.
  - **Logic:**
    1. Uses the `useSession` hook from `next-auth/react` to get session data.
    2. If the session is loading, renders a loading spinner.
    3. If there is no session (user is not logged in), redirects to `/login`.
    4. If a session exists but the user's role is not in `allowedRoles`, redirects to `/unauthorized`.
    5. If the user is authenticated and has the correct role, renders the `children`.

## 7. Authentication & Authorization
- **Authentication Strategy:** The application uses **JWT-based session management** facilitated by Auth.js.
  - **Credentials Flow:**
    1. User submits email/password to `/api/auth/login`.
    2. The `CredentialsProvider`'s `authorize` function validates credentials against the database.
    3. On success, Auth.js creates a JWT containing user info (`id`, `role`, `companyId`).
    4. This JWT is stored in a secure, HTTP-only cookie (`next-auth.session-token`).
  - **Google OAuth 2.0 Flow:**
    1. User clicks "Sign in with Google".
    2. They are redirected to Google's consent screen.
    3. After approval, Google redirects back to `/api/auth/callback/google`.
    4. The `signIn` callback in `auth-config.ts` checks if the user exists in the database. If not, it creates a new user with a default 'Staff' role.
    5. Auth.js then generates a JWT and session cookie, same as the credentials flow.
- **Authorization/Roles:**
  - **`Admin`:** Full access to all API routes and UI components, including administrative dashboards for managing users, companies, and system settings.
  - **`CrewChief`:** Can view and manage shifts they are assigned to, including clocking in/out workers. Has a specific dashboard view.
  - **`Employee` / `Staff`:** Can view their own assigned shifts and clock in/out. Has the most restricted dashboard view.
  - **`CompanyUser`:** A role likely intended for client-side users to view job progress, though implementation details would need further analysis.
  - Authorization is enforced in API routes by checking `session.user.role` and on the frontend using the `withAuth` HOC or `AuthGuard` component.

## 8. Environment Configuration
- **File:** `.env`
- **Contents:**
```
# This file contains secrets and should not be committed to version control.
# Use .env.example as a template.

# Database connection string for Prisma
DATABASE_URL="postgres://postgres:postgres@localhost:51214/template1?sslmode=disable&connection_limit=1&connect_timeout=0&max_idle_connection_lifetime=0&pool_timeout=0&single_use_connections=true&socket_timeout=0"
DATABASE_URL_DIRECT="postgres://postgres:postgres@localhost:51214/template1?sslmode=disable&connection_limit=1&connect_timeout=0&max_idle_connection_lifetime=0&pool_timeout=0&single_use_connections=true&socket_timeout=0"

# Application Environment
NODE_ENV="development"
NEXT_TELEMETRY_DISABLED="1"

# Auth.js Configuration
# A random secret used to sign session cookies and tokens.
NEXTAUTH_SECRET="uivbienbuivenbivrenbiiinbrenbirenbiivrevnurnei"
# The canonical URL of the application.
NEXTAUTH_URL="http://localhost:3000"

# Google OAuth Credentials (obtain from Google Cloud Console)
GOOGLE_CLIENT_ID="242147063579-mjqa4nji7lc9fvn7nrpf77r4dej1t18s.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET="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"

# JWT Secret (can be the same as NEXTAUTH_SECRET)
JWT_SECRET="uivbienbuivenbivrenujujubiiinbrenbirenbiivrevnurnei"

# Email Configuration (for a service like SendGrid or Gmail SMTP)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="bhxfntiblfatdlep"

# Google AI/API Keys (obtain from Google Cloud Console)
GOOGLE_AI_API_KEY="AIzaSyDZ2W8uqVhvXm38NqWSXmXneREt4UilG_A"
GOOGLE_API_KEY="AIzaSyAaMQ6qq0iVnyt2w1IERTPwXGrllSLnhZQ"
```

## 9. Build, Deployment, & Operations
- **Build Process:**
  - **Command:** `npm run build`
  - **Action:** This script executes `next build`, which transpiles TypeScript to JavaScript, bundles all assets (CSS, images), minifies the code for production, and generates optimized static pages where possible.
- **Deployment:**
  - The project includes a `Dockerfile` and a `docker-compose.yml`, indicating it is designed to be deployed as a Docker container.
  - The `deploy-cloud-run.sh` and `cloudbuild.yaml` files show that the intended deployment target is **Google Cloud Run**, a serverless container platform.
  - **CI/CD Pipeline (via `cloudbuild.yaml`):**
    1.  **Build:** Uses a Node.js buildpack to run `npm install` and `npm run build`.
    2.  **Package:** Packages the application into a Docker image.
    3.  **Push:** Pushes the Docker image to Google Artifact Registry.
    4.  **Deploy:** Deploys the new image to the specified Cloud Run service.
- **Running the Application:**
  - **Development:** `npm run dev` (starts the Next.js development server with hot-reloading).
  - **Production:** `npm start` (starts the optimized production server after running `npm run build`).
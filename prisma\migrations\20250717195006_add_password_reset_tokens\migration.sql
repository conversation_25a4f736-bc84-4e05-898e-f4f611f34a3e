/*
  Warnings:

  - You are about to drop the column `approvedAt` on the `timesheets` table. All the data in the column will be lost.
  - You are about to drop the column `approvedById` on the `timesheets` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[name,companyId]` on the table `jobs` will be added. If there are existing duplicate values, this will fail.

*/
-- DropIndex
DROP INDEX "announcements_createdById_idx";

-- DropIndex
DROP INDEX "announcements_date_idx";

-- DropIndex
DROP INDEX "notifications_createdAt_idx";

-- DropIndex
DROP INDEX "notifications_isRead_idx";

-- DropIndex
DROP INDEX "notifications_userId_idx";

-- DropIndex
DROP INDEX "worker_requirements_shiftId_idx";

-- AlterTable
ALTER TABLE "jobs" ADD COLUMN     "status" TEXT NOT NULL DEFAULT 'Pending';

-- AlterTable
ALTER TABLE "timesheets" DROP COLUMN "approvedAt",
DROP COLUMN "approvedById",
ALTER COLUMN "status" SET DEFAULT 'draft';

-- CreateTable
CREATE TABLE "password_reset_tokens" (
    "id" TEXT NOT NULL,
    "token" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "expiresAt" TIMESTAMP(3) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "password_reset_tokens_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "password_reset_tokens_token_key" ON "password_reset_tokens"("token");

-- CreateIndex
CREATE UNIQUE INDEX "jobs_name_companyId_key" ON "jobs"("name", "companyId");

-- AddForeignKey
ALTER TABLE "password_reset_tokens" ADD CONSTRAINT "password_reset_tokens_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

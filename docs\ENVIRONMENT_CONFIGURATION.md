# Environment Configuration Guide

This document provides comprehensive information about all environment variables required for the HoliTime workforce management application.

## Table of Contents
- [Quick Setup](#quick-setup)
- [Environment Files](#environment-files)
- [Required Variables](#required-variables)
- [Optional Variables](#optional-variables)
- [Environment-Specific Configurations](#environment-specific-configurations)
- [Security Considerations](#security-considerations)
- [Troubleshooting](#troubleshooting)

## Quick Setup

1. Copy the appropriate environment template:
   ```bash
   cp .env.example .env
   ```

2. Fill in the required values (see [Required Variables](#required-variables))

3. Verify your configuration:
   ```bash
   npm run env:check
   ```

## Environment Files

The application uses different environment files for different contexts:

- `.env` - Local development (git-ignored)
- `.env.example` - Template file (committed to git)
- `.env.development` - Development environment defaults
- `.env.production` - Production environment configuration
- `.env.test` - Test environment configuration

## Required Variables

### Core Application
```bash
# Application Environment
NODE_ENV="development" # or "production", "test"
NEXT_TELEMETRY_DISABLED="1" # Disable Next.js telemetry

# Application URL (must match your deployment URL)
NEXTAUTH_URL="http://localhost:3000" # Development
# NEXTAUTH_URL="https://your-domain.com" # Production
```

### Database Configuration
```bash
# Primary database connection (required)
DATABASE_URL="postgresql://username:password@host:port/database?sslmode=require"

# Direct database connection (for migrations and admin operations)
DATABASE_URL_DIRECT="postgresql://username:password@host:port/database?sslmode=require"
```

**Database URL Format Examples:**
- Local PostgreSQL: `postgresql://postgres:password@localhost:5432/holitime`
- Aiven Cloud: `postgresql://user:pass@host:port/defaultdb?sslmode=require`
- Supabase: `postgresql://postgres:<EMAIL>:5432/postgres`

### Authentication & Security
```bash
# NextAuth.js secret (generate with: openssl rand -base64 32)
NEXTAUTH_SECRET="your-secret-key-here"

# JWT secret for custom token operations
JWT_SECRET="your-jwt-secret-here"
```

### Google OAuth & APIs
```bash
# Google OAuth credentials (from Google Cloud Console)
GOOGLE_CLIENT_ID="your-client-id.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET="your-client-secret"

# Google API key for Sheets/Drive integration
GOOGLE_API_KEY="your-google-api-key"

# Google AI API key for Gemini integration
GOOGLE_AI_API_KEY="your-google-ai-api-key"
```

## Optional Variables

### Email Configuration (SMTP)
```bash
# Email service configuration
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"

# Alternative email service examples:
# SendGrid:
# SMTP_HOST="smtp.sendgrid.net"
# SMTP_PORT="587"
# SMTP_USER="apikey"
# SMTP_PASS="your-sendgrid-api-key"
```

### Real-time Features
```bash
# WebSocket URL for real-time features
NEXT_PUBLIC_WS_URL="ws://localhost:3000/api/ws" # Development
# NEXT_PUBLIC_WS_URL="wss://your-domain.com/api/ws" # Production
```

### File Storage (Optional)
```bash
# AWS S3 configuration (if using S3 for file storage)
AWS_ACCESS_KEY_ID="your-access-key"
AWS_SECRET_ACCESS_KEY="your-secret-key"
AWS_REGION="us-east-1"
AWS_S3_BUCKET="your-bucket-name"

# Google Cloud Storage (alternative)
GOOGLE_CLOUD_PROJECT_ID="your-project-id"
GOOGLE_CLOUD_STORAGE_BUCKET="your-bucket-name"
```

### Monitoring & Analytics (Optional)
```bash
# Sentry for error tracking
SENTRY_DSN="your-sentry-dsn"

# Google Analytics
NEXT_PUBLIC_GA_ID="G-XXXXXXXXXX"

# Application monitoring
NEXT_PUBLIC_APP_VERSION="1.0.0"
```

## Environment-Specific Configurations

### Development Environment
```bash
NODE_ENV="development"
NEXTAUTH_URL="http://localhost:3000"
DATABASE_URL="postgresql://postgres:password@localhost:5432/holitime_dev"
# Use local database for development
```

### Production Environment
```bash
NODE_ENV="production"
NEXTAUTH_URL="https://your-production-domain.com"
DATABASE_URL="postgresql://user:pass@prod-host:port/database?sslmode=require"
# Use production database with SSL
```

### Test Environment
```bash
NODE_ENV="test"
NEXTAUTH_URL="http://localhost:3000"
DATABASE_URL="postgresql://postgres:password@localhost:5432/holitime_test"
# Use separate test database
```

## Security Considerations

### Secret Generation
Generate secure secrets using:
```bash
# For NEXTAUTH_SECRET and JWT_SECRET
openssl rand -base64 32

# Alternative using Node.js
node -e "console.log(require('crypto').randomBytes(32).toString('base64'))"
```

### Environment Variable Security
1. **Never commit `.env` files** to version control
2. **Use different secrets** for each environment
3. **Rotate secrets regularly** in production
4. **Use environment-specific databases** to prevent data leaks
5. **Enable SSL/TLS** for all database connections in production

### Google OAuth Setup
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing
3. Enable Google+ API and Google Drive API
4. Create OAuth 2.0 credentials
5. Add authorized redirect URIs:
   - Development: `http://localhost:3000/api/auth/callback/google`
   - Production: `https://your-domain.com/api/auth/callback/google`

## Configuration Validation

The application includes built-in environment validation. Missing or invalid variables will cause startup errors with helpful messages.

### Manual Validation
Check your environment configuration:
```bash
# Development check
curl http://localhost:3000/api/debug/environment

# Or use the built-in validation
npm run env:validate
```

### Required Variable Checklist
- [ ] `NODE_ENV` is set correctly
- [ ] `DATABASE_URL` is valid and accessible
- [ ] `NEXTAUTH_SECRET` is set and secure
- [ ] `NEXTAUTH_URL` matches your deployment URL
- [ ] Google OAuth credentials are configured
- [ ] SMTP settings are configured (if using email features)

## Troubleshooting

### Common Issues

**Database Connection Errors:**
- Verify `DATABASE_URL` format and credentials
- Check network connectivity to database host
- Ensure SSL mode matches database requirements

**Authentication Issues:**
- Verify `NEXTAUTH_SECRET` is set
- Check `NEXTAUTH_URL` matches your domain
- Confirm Google OAuth redirect URIs are correct

**Google API Errors:**
- Verify API keys are valid and have correct permissions
- Check that required APIs are enabled in Google Cloud Console
- Ensure OAuth consent screen is configured

**Email Delivery Issues:**
- Verify SMTP credentials and server settings
- Check if app passwords are required (Gmail)
- Test SMTP connection independently

### Environment Debugging
```bash
# Check environment variables (development only)
GET /api/debug/environment

# Test database connection
GET /api/debug/db-test

# Verify NextAuth configuration
GET /api/debug/nextauth
```

### Production Deployment
For production deployments, ensure:
1. All secrets are stored securely (not in code)
2. Database uses SSL connections
3. `NEXTAUTH_URL` uses HTTPS
4. CORS settings are properly configured
5. Rate limiting is enabled for API endpoints

## Example .env.example File
```bash
# Copy this file to .env and fill in your values

# Application
NODE_ENV="development"
NEXT_TELEMETRY_DISABLED="1"
NEXTAUTH_URL="http://localhost:3000"

# Database
DATABASE_URL="postgresql://postgres:password@localhost:5432/holitime"
DATABASE_URL_DIRECT="postgresql://postgres:password@localhost:5432/holitime"

# Authentication
NEXTAUTH_SECRET="your-nextauth-secret-here"
JWT_SECRET="your-jwt-secret-here"

# Google OAuth & APIs
GOOGLE_CLIENT_ID="your-client-id.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET="your-client-secret"
GOOGLE_API_KEY="your-google-api-key"
GOOGLE_AI_API_KEY="your-google-ai-api-key"

# Email (Optional)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"

# Real-time (Optional)
NEXT_PUBLIC_WS_URL="ws://localhost:3000/api/ws"
```

For additional help, refer to the main README.md or contact the development team.

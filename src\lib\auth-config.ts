import { NextAuthOptions } from 'next-auth';
import GoogleProvider from 'next-auth/providers/google';
import CredentialsProvider from 'next-auth/providers/credentials';
import { authenticateUser, createUser, getUserByEmail } from './auth';

if (!process.env.NEXTAUTH_SECRET) {
  throw new Error('NEXTAUTH_SECRET environment variable is not set');
}

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        try {
          const user = await authenticateUser(
            credentials.email,
            credentials.password
          );

          if (user) {
            return {
              id: user.id,
              email: user.email,
              name: user.name,
              role: user.role,
              companyId: user.companyId || undefined,
              avatarUrl: user.avatarUrl,
            };
          }
          return null;
        } catch (error) {
          console.error('Credentials auth error:', error);
          return null;
        }
      },
    }),
    ...(process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET
      ? [
          GoogleProvider({
            clientId: process.env.GOOGLE_CLIENT_ID,
            clientSecret: process.env.GOOGLE_CLIENT_SECRET,
            authorization: {
              params: {
                prompt: "consent",
                access_type: "offline",
                response_type: "code",
              },
            },
          }),
        ]
      : []),
  ],
  callbacks: {
    async signIn({ user, account }) {
      console.log("SIGN-IN CALLBACK TRIGGERED");
      console.log("Account provider:", account?.provider);
      console.log("User email:", user.email);

      if (account?.provider === 'google') {
        if (!user.email) {
          console.error('Google sign-in error: Email is missing from Google profile.');
          return false;
        }

        try {
          console.log(`Attempting to find user with email: ${user.email}`);
          const existingUser = await getUserByEmail(user.email);
          console.log("Database lookup completed.");

          if (!existingUser) {
            console.log(`No existing user found. Attempting to create new user for ${user.email}.`);
            if (!user.name) {
              console.error('Google sign-in error: User name is missing from Google profile.');
              return false;
            }
            await createUser({
              email: user.email,
              name: user.name,
              password: '', // No password for OAuth users
              role: 'Staff', // Default role
            });
            console.log(`Successfully created new user: ${user.email}`);
          } else {
            console.log(`Existing user found: ${existingUser.email}`);
          }
        } catch (error) {
          console.error('DATABASE ERROR DURING SIGN-IN:', error);
          console.error(JSON.stringify(error, null, 2));
          return false;
        }
      }
      
      // For Google OAuth users, we need to get the user data from database to check role and companyId
      if (account?.provider === 'google') {
        try {
          const dbUser = await getUserByEmail(user.email!);
          if (dbUser && dbUser.role !== 'Admin' && dbUser.role !== 'Staff' && !dbUser.companyId) {
            console.error(
              `Sign-in denied for user ${user.email}: No companyId associated with non-admin account.`
            );
            return false;
          }
        } catch (error) {
          console.error('Error checking user role during sign-in:', error);
          return false;
        }
      } else {
        // For credentials login, check the user object directly
        if (user.role && user.role !== 'Admin' && user.role !== 'Staff' && !user.companyId) {
          console.error(
            `Sign-in denied for user ${user.email}: No companyId associated with non-admin account.`
          );
          return false;
        }
      }
      return true;
    },

    async jwt({ token, user, account }) {
      if (account && user) {
        token.id = user.id;
        token.role = user.role;
        token.companyId = user.companyId ?? undefined;
        token.avatarUrl = user.avatarUrl ?? undefined;
      }
      return token;
    },

    async session({ session, token }) {
      if (token) {
        session.user.id = token.id as string;
        session.user.role = token.role;
        session.user.companyId = token.companyId;
        session.user.avatarUrl = token.avatarUrl as string | undefined;
      }
      return session;
    },
  },
  session: {
    strategy: 'jwt',
    maxAge: 7 * 24 * 60 * 60, // 7 days
  },
  secret: process.env.NEXTAUTH_SECRET,
  cookies: {
    sessionToken: {
      name:
        process.env.NODE_ENV === 'production'
          ? '__Secure-next-auth.session-token'
          : 'next-auth.session-token',
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production',
      },
    },
  },
};

"use client"

import { useUser } from '@/hooks/use-user';
import { Clock, Users, Briefcase, Plus, Calendar } from 'lucide-react';

export default function DashboardPage() {
  const { user } = useUser();

  return (
    <div className="mobile-container">
      {/* Header */}
      <div className="mb-6">
        <h1 className="mobile-heading-primary">
          Welcome back, {user?.name || 'User'}!
        </h1>
        <p className="text-muted-foreground">
          {new Date().toLocaleDateString('en-US', { 
            weekday: 'long', 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
          })}
        </p>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
        <div className="mobile-card bg-gradient-to-r from-blue-500 to-blue-600 text-white border-0">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-blue-100 text-sm">Active Shifts</p>
              <p className="text-3xl font-bold">3</p>
            </div>
            <Clock className="h-8 w-8 text-blue-200" />
          </div>
        </div>
        
        <div className="mobile-card bg-gradient-to-r from-green-500 to-green-600 text-white border-0">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-green-100 text-sm">Today's Jobs</p>
              <p className="text-3xl font-bold">7</p>
            </div>
            <Briefcase className="h-8 w-8 text-green-200" />
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="mobile-card mb-6">
        <h2 className="mobile-heading-secondary">Quick Actions</h2>
        <div className="grid grid-cols-2 gap-3">
          <button className="btn btn-primary flex flex-col items-center gap-2 py-6">
            <Plus size={24} />
            <span>New Shift</span>
          </button>
          <button className="btn btn-outline flex flex-col items-center gap-2 py-6">
            <Users size={24} />
            <span>View Team</span>
          </button>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="mobile-card">
        <h2 className="mobile-heading-secondary">Recent Activity</h2>
        <div className="space-y-3">
          <div className="flex items-center gap-3 p-3 bg-muted rounded-lg">
            <Calendar size={16} className="text-muted-foreground" />
            <div className="flex-1">
              <p className="text-sm font-medium">Shift started at Construction Site A</p>
              <p className="text-xs text-muted-foreground">2 hours ago</p>
            </div>
          </div>
          <div className="flex items-center gap-3 p-3 bg-muted rounded-lg">
            <Users size={16} className="text-muted-foreground" />
            <div className="flex-1">
              <p className="text-sm font-medium">New employee assigned to Project B</p>
              <p className="text-xs text-muted-foreground">4 hours ago</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

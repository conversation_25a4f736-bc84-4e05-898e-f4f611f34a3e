'use client';

import { useUser } from '@/hooks/use-user';
import { useApi } from '@/hooks/use-api';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Calendar, Briefcase, Building2, ChevronDown, ChevronUp } from 'lucide-react';
import ShiftManager from '@/components/dashboard/shift-management/shift-manager';
import { ShiftWithDetails } from '@/components/dashboard/shift-management/types';

type Shift = {
  id: string;
  jobName: string;
  clientName: string;
  date: string;
  status: string;
};

export default function ManagerDashboard() {
  const { user } = useUser();
  const router = useRouter();
  const [expandedShift, setExpandedShift] = useState<string | null>(null);

  const { data: shiftsData, loading: shiftsLoading, error: shiftsError, refetch: refetchShifts } = useApi<{ shifts: Shift[] }>('/api/shifts?filter=active');
  const { data: shiftDetails, loading: shiftDetailsLoading, error: shiftDetailsError, refetch: refetchShiftDetails } = useApi<ShiftWithDetails>(
    expandedShift ? `/api/shifts/${expandedShift}/details` : null
  );

  useEffect(() => {
    refetchShifts();
  }, [refetchShifts]);

  const handleToggleShift = (shiftId: string) => {
    if (expandedShift === shiftId) {
      setExpandedShift(null);
    } else {
      setExpandedShift(shiftId);
    }
  };

  if (shiftsLoading) {
    return <div>Loading dashboard data...</div>;
  }

  if (shiftsError) {
    return <div className="text-red-500">Error loading dashboard data.</div>;
  }

  const shifts = shiftsData?.shifts || [];

  return (
    <div className="space-y-6">
      <header>
        <h1 className="text-3xl font-bold">Welcome, {user?.name}!</h1>
        <p className="text-muted-foreground">Manager Dashboard Overview</p>
      </header>

      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Shifts</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{shifts.length}</div>
          </CardContent>
        </Card>
      </div>

      <section>
        <h2 className="text-2xl font-semibold mb-4">Active Shifts</h2>
        {shifts.length > 0 ? (
          <div className="space-y-4">
            {shifts.map((shift) => (
              <Card key={shift.id}>
                <CardHeader>
                  <div className="flex justify-between items-center">
                    <div>
                      <CardTitle>{shift.jobName}</CardTitle>
                      <CardDescription>{shift.clientName} - {new Date(shift.date).toLocaleDateString()}</CardDescription>
                    </div>
                    <div className="flex items-center gap-4">
                      <Badge>{shift.status}</Badge>
                      <Button variant="outline" size="sm" onClick={() => handleToggleShift(shift.id)}>
                        {expandedShift === shift.id ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                {expandedShift === shift.id && (
                  <CardContent>
                    {shiftDetailsLoading && <p>Loading shift details...</p>}
                    {shiftDetailsError && <p className="text-red-500">Error loading details.</p>}
                    {shiftDetails && (
                      <ShiftManager
                        shift={shiftDetails}
                        onUpdate={() => refetchShiftDetails()}
                      />
                    )}
                  </CardContent>
                )}
              </Card>
            ))}
          </div>
        ) : (
          <p>No active shifts found.</p>
        )}
      </section>
    </div>
  );
}

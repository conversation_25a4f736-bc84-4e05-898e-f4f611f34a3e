import { prisma } from '../prisma';
import type { WorkerRequirement } from '@prisma/client';

export async function getWorkerRequirements(shiftId: string): Promise<WorkerRequirement[]> {
  try {
    return await prisma.workerRequirement.findMany({
      where: { shiftId },
      orderBy: { role_code: 'asc' },
    });
  } catch (error) {
    console.error('Error getting worker requirements:', error);
    return [];
  }
}

export async function updateWorkerRequirements(
  shiftId: string,
  requirements: { roleCode: string; requiredCount: number }[]
): Promise<boolean> {
  try {
    await prisma.$transaction(async (tx) => {
      await tx.workerRequirement.deleteMany({
        where: { shiftId },
      });

      await tx.workerRequirement.createMany({
        data: requirements.map((req) => ({
          shiftId,
          role_code: req.roleCode,
          required_count: req.requiredCount,
        })),
      });

      const totalRequired = requirements.reduce((acc, req) => acc + req.requiredCount, 0);

      await tx.shift.update({
        where: { id: shiftId },
        data: { requested_workers: totalRequired },
      });
    });
    return true;
  } catch (error) {
    console.error('Error updating worker requirements:', error);
    return false;
  }
}

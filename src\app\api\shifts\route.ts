import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/middleware';
import { getAllShifts, createShift, getShiftsByCrewChief } from '@/lib/services/shifts';
import { UserRole, User } from '@prisma/client';

export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const options: any = {
      page: parseInt(searchParams.get('page') || '1'),
      pageSize: parseInt(searchParams.get('pageSize') || '50'),
      status: searchParams.getAll('status'),
      startDate: searchParams.get('startDate'),
      endDate: searchParams.get('endDate'),
      jobId: searchParams.get('jobId'),
      clientId: searchParams.get('clientId'),
    };

    if (user.role === 'CrewChief') {
      const shifts = await getShiftsByCrewChief(user as User);
      return NextResponse.json({
        success: true,
        shifts,
        total: shifts.length,
        pages: 1,
      });
    }

    if (user.role === UserRole.CompanyUser) {
      options.clientId = options.clientId || user.companyId;
    }

    const { shifts, total } = await getAllShifts(options);

    if (user.role === UserRole.Staff) {
      const filteredShifts = shifts.filter((shift: { assignedPersonnel: { userId: string }[] }) =>
        shift.assignedPersonnel.some((person: { userId: string }) => 
          person.userId === user.id
        )
      );
      return NextResponse.json({
        success: true,
      shifts: filteredShifts,
      total: filteredShifts.length,
      pages: 1,
      });
    }

    return NextResponse.json({
      success: true,
      shifts,
      total,
      pages: Math.ceil(total / options.pageSize),
    });
  } catch (error) {
    console.error('Error getting shifts:', error);
    const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
    return NextResponse.json(
      { error: 'Internal server error', details: errorMessage },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    if (!user || (user.role !== UserRole.Admin)) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { jobId, date, startTime, endTime } = body;

    if (!jobId || !date || !startTime || !endTime) {
      return NextResponse.json(
        { error: 'Job, date, start time, and end time are required' },
        { status: 400 }
      );
    }

    const shift = await createShift(user as User, body);

    return NextResponse.json({
      success: true,
      shift,
    });
  } catch (error) {
    console.error('Error creating shift:', error);
    const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
    return NextResponse.json(
      { error: 'Internal server error', details: errorMessage },
      { status: 500 }
    );
  }
}

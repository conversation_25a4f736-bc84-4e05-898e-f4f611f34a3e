/**
 * Tests for authorization utilities
 */

import { hasPermission, getRoleHierarchy, canUserAccessResource } from '../authorization'
import { UserRole } from '@prisma/client'

// Mock user data
const mockUsers = {
  admin: {
    id: 'admin-1',
    role: 'Admin' as User<PERSON><PERSON>,
    companyId: null,
  },
  staff: {
    id: 'staff-1',
    role: 'Staff' as UserRole,
    companyId: null,
  },
  crewChief: {
    id: 'crew-chief-1',
    role: 'CrewChief' as User<PERSON><PERSON>,
    companyId: null,
  },
  employee: {
    id: 'employee-1',
    role: 'Employee' as UserRole,
    companyId: null,
  },
  companyUser: {
    id: 'company-user-1',
    role: 'CompanyUser' as UserRole,
    companyId: 'company-1',
  },
}

describe('Authorization System', () => {
  describe('getRoleHierarchy', () => {
    it('should return correct hierarchy levels', () => {
      expect(getRoleHierarchy('Admin')).toBe(4)
      expect(getRoleHierarchy('Staff')).toBe(3)
      expect(getRoleHierarchy('CrewChief')).toBe(2)
      expect(getRoleHierarchy('CompanyUser')).toBe(2)
      expect(getRoleHierarchy('Employee')).toBe(1)
    })

    it('should return 0 for invalid role', () => {
      expect(getRoleHierarchy('InvalidRole' as UserRole)).toBe(0)
    })
  })

  describe('hasPermission', () => {
    describe('User Management', () => {
      it('should allow admin to create users', () => {
        expect(hasPermission(mockUsers.admin, 'USER', 'CREATE')).toBe(true)
      })

      it('should allow staff to create users', () => {
        expect(hasPermission(mockUsers.staff, 'USER', 'CREATE')).toBe(true)
      })

      it('should not allow employee to create users', () => {
        expect(hasPermission(mockUsers.employee, 'USER', 'CREATE')).toBe(false)
      })

      it('should allow admin to delete users', () => {
        expect(hasPermission(mockUsers.admin, 'USER', 'DELETE')).toBe(true)
      })

      it('should not allow staff to delete users', () => {
        expect(hasPermission(mockUsers.staff, 'USER', 'DELETE')).toBe(false)
      })

      it('should allow users to read their own profile', () => {
        expect(hasPermission(mockUsers.employee, 'USER', 'READ', {
          resourceId: 'employee-1'
        })).toBe(true)
      })

      it('should not allow users to read other profiles', () => {
        expect(hasPermission(mockUsers.employee, 'USER', 'READ', {
          resourceId: 'other-user-id'
        })).toBe(false)
      })
    })

    describe('Job Management', () => {
      it('should allow admin to manage jobs', () => {
        expect(hasPermission(mockUsers.admin, 'JOB', 'CREATE')).toBe(true)
        expect(hasPermission(mockUsers.admin, 'JOB', 'UPDATE')).toBe(true)
        expect(hasPermission(mockUsers.admin, 'JOB', 'DELETE')).toBe(true)
      })

      it('should allow staff to manage jobs', () => {
        expect(hasPermission(mockUsers.staff, 'JOB', 'CREATE')).toBe(true)
        expect(hasPermission(mockUsers.staff, 'JOB', 'UPDATE')).toBe(true)
        expect(hasPermission(mockUsers.staff, 'JOB', 'DELETE')).toBe(false)
      })

      it('should not allow employees to manage jobs', () => {
        expect(hasPermission(mockUsers.employee, 'JOB', 'CREATE')).toBe(false)
        expect(hasPermission(mockUsers.employee, 'JOB', 'UPDATE')).toBe(false)
        expect(hasPermission(mockUsers.employee, 'JOB', 'DELETE')).toBe(false)
      })

      it('should allow company users to read their company jobs', () => {
        const jobResource = { companyId: 'company-1' }
        expect(hasPermission(mockUsers.companyUser, 'JOB', 'READ', {
          resource: jobResource
        })).toBe(true)
      })

      it('should not allow company users to read other company jobs', () => {
        const jobResource = { companyId: 'company-2' }
        expect(hasPermission(mockUsers.companyUser, 'JOB', 'READ', {
          resource: jobResource
        })).toBe(false)
      })
    })

    describe('Shift Management', () => {
      it('should allow crew chiefs to create shifts', () => {
        expect(hasPermission(mockUsers.crewChief, 'SHIFT', 'CREATE')).toBe(true)
      })

      it('should allow crew chiefs to update shifts', () => {
        expect(hasPermission(mockUsers.crewChief, 'SHIFT', 'UPDATE')).toBe(true)
      })

      it('should not allow employees to create shifts', () => {
        expect(hasPermission(mockUsers.employee, 'SHIFT', 'CREATE')).toBe(false)
      })

      it('should allow employees to read shifts', () => {
        expect(hasPermission(mockUsers.employee, 'SHIFT', 'READ')).toBe(true)
      })
    })

    describe('Timesheet Management', () => {
      it('should allow users to submit their own timesheets', () => {
        const timesheetResource = { submittedBy: 'employee-1' }
        expect(hasPermission(mockUsers.employee, 'TIMESHEET', 'SUBMIT', {
          resource: timesheetResource
        })).toBe(true)
      })

      it('should not allow users to submit others timesheets', () => {
        const timesheetResource = { submittedBy: 'other-user-id' }
        expect(hasPermission(mockUsers.employee, 'TIMESHEET', 'SUBMIT', {
          resource: timesheetResource
        })).toBe(false)
      })

      it('should allow company users to approve timesheets', () => {
        expect(hasPermission(mockUsers.companyUser, 'TIMESHEET', 'APPROVE')).toBe(true)
      })

      it('should allow staff to approve timesheets', () => {
        expect(hasPermission(mockUsers.staff, 'TIMESHEET', 'APPROVE')).toBe(true)
      })

      it('should not allow employees to approve timesheets', () => {
        expect(hasPermission(mockUsers.employee, 'TIMESHEET', 'APPROVE')).toBe(false)
      })
    })

    describe('Announcement Management', () => {
      it('should allow admin to create announcements', () => {
        expect(hasPermission(mockUsers.admin, 'ANNOUNCEMENT', 'CREATE')).toBe(true)
      })

      it('should not allow employees to create announcements', () => {
        expect(hasPermission(mockUsers.employee, 'ANNOUNCEMENT', 'CREATE')).toBe(false)
      })

      it('should allow all users to read announcements', () => {
        expect(hasPermission(mockUsers.admin, 'ANNOUNCEMENT', 'READ')).toBe(true)
        expect(hasPermission(mockUsers.staff, 'ANNOUNCEMENT', 'READ')).toBe(true)
        expect(hasPermission(mockUsers.crewChief, 'ANNOUNCEMENT', 'READ')).toBe(true)
        expect(hasPermission(mockUsers.employee, 'ANNOUNCEMENT', 'READ')).toBe(true)
        expect(hasPermission(mockUsers.companyUser, 'ANNOUNCEMENT', 'READ')).toBe(true)
      })
    })

    describe('Company Management', () => {
      it('should allow admin to manage companies', () => {
        expect(hasPermission(mockUsers.admin, 'COMPANY', 'CREATE')).toBe(true)
        expect(hasPermission(mockUsers.admin, 'COMPANY', 'UPDATE')).toBe(true)
        expect(hasPermission(mockUsers.admin, 'COMPANY', 'DELETE')).toBe(true)
      })

      it('should not allow non-admin to manage companies', () => {
        expect(hasPermission(mockUsers.staff, 'COMPANY', 'CREATE')).toBe(false)
        expect(hasPermission(mockUsers.employee, 'COMPANY', 'CREATE')).toBe(false)
        expect(hasPermission(mockUsers.companyUser, 'COMPANY', 'CREATE')).toBe(false)
      })

      it('should allow company users to read their own company', () => {
        const companyResource = { id: 'company-1' }
        expect(hasPermission(mockUsers.companyUser, 'COMPANY', 'READ', {
          resource: companyResource
        })).toBe(true)
      })
    })

    describe('Edge Cases', () => {
      it('should handle undefined user', () => {
        expect(hasPermission(undefined as any, 'USER', 'READ')).toBe(false)
      })

      it('should handle null user', () => {
        expect(hasPermission(null as any, 'USER', 'READ')).toBe(false)
      })

      it('should handle invalid resource', () => {
        expect(hasPermission(mockUsers.admin, 'INVALID_RESOURCE' as any, 'READ')).toBe(false)
      })

      it('should handle invalid action', () => {
        expect(hasPermission(mockUsers.admin, 'USER', 'INVALID_ACTION' as any)).toBe(false)
      })

      it('should handle missing resource context when required', () => {
        expect(hasPermission(mockUsers.companyUser, 'JOB', 'READ')).toBe(false)
      })
    })
  })

  describe('canUserAccessResource', () => {
    it('should allow admin to access any resource', () => {
      expect(canUserAccessResource(mockUsers.admin, 'any-resource-id', 'USER')).toBe(true)
    })

    it('should allow user to access their own resources', () => {
      expect(canUserAccessResource(mockUsers.employee, 'employee-1', 'USER')).toBe(true)
    })

    it('should not allow user to access other user resources', () => {
      expect(canUserAccessResource(mockUsers.employee, 'other-user-id', 'USER')).toBe(false)
    })

    it('should allow company user to access company resources', () => {
      const companyResource = { companyId: 'company-1' }
      expect(canUserAccessResource(mockUsers.companyUser, 'resource-id', 'JOB', companyResource)).toBe(true)
    })

    it('should not allow company user to access other company resources', () => {
      const companyResource = { companyId: 'company-2' }
      expect(canUserAccessResource(mockUsers.companyUser, 'resource-id', 'JOB', companyResource)).toBe(false)
    })
  })
})

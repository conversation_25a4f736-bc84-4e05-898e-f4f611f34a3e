#!/bin/bash

# HoliTime Cloud Run Deployment Script
# This script builds and deploys the HoliTime application to Google Cloud Run

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ID=${PROJECT_ID:-"elated-fabric-460119-t3"}
REGION=${REGION:-"us-west2"}
SERVICE_NAME=${SERVICE_NAME:-"holitime"}
IMAGE_NAME="gcr.io/${PROJECT_ID}/${SERVICE_NAME}"

# Functions
print_step() {
    echo -e "${BLUE}==>${NC} $1"
}

print_success() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

check_prerequisites() {
    print_step "Checking prerequisites..."
    
    # Check if gcloud is installed
    if ! command -v gcloud &> /dev/null; then
        print_error "gcloud CLI is not installed. Please install it first."
        exit 1
    fi
    
    # Check if docker is installed
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install it first."
        exit 1
    fi
    
    # Check if user is authenticated
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
        print_error "Not authenticated with gcloud. Run 'gcloud auth login' first."
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

setup_project() {
    print_step "Setting up Google Cloud project..."
    
    # Set the project
    gcloud config set account "<EMAIL>"
    gcloud config set project $PROJECT_ID
    
    # Enable required APIs
    print_step "Enabling required APIs..."
    gcloud services enable cloudbuild.googleapis.com
    gcloud services enable run.googleapis.com
    gcloud services enable containerregistry.googleapis.com
    gcloud services enable secretmanager.googleapis.com
    
    print_success "Project setup completed"
}

create_secrets() {
    print_step "Creating secrets in Secret Manager..."
    
    # List of required secrets
    secrets=(
        "DATABASE_URL"
        "NEXTAUTH_SECRET"
        "JWT_SECRET"
        "GOOGLE_CLIENT_ID"
        "GOOGLE_CLIENT_SECRET"
    )
    
    for secret in "${secrets[@]}"; do
        if gcloud secrets describe $secret &> /dev/null; then
            print_warning "Secret $secret already exists, skipping..."
        else
            echo -n "Enter value for $secret: "
            read -s secret_value
            echo
            echo -n "$secret_value" | gcloud secrets create $secret --data-file=-
            print_success "Created secret: $secret"
        fi
    done
}

build_and_push_image() {
    print_step "Building and pushing Docker image..."
    
    # Build the image
    docker build -t $IMAGE_NAME:latest .
    
    # Configure Docker to use gcloud as a credential helper
    gcloud auth configure-docker
    
    # Push the image
    docker push $IMAGE_NAME:latest
    
    print_success "Image built and pushed successfully"
}

deploy_to_cloud_run() {
    print_step "Deploying to Cloud Run..."
    
    gcloud run deploy $SERVICE_NAME \
        --image $IMAGE_NAME:latest \
        --region $REGION \
        --platform managed \
        --allow-unauthenticated \
        --port 3000 \
        --memory 2Gi \
        --cpu 2 \
        --max-instances 10 \
        --min-instances 1 \
        --concurrency 80 \
        --timeout 300 \
        --set-env-vars "NODE_ENV=production,NEXT_TELEMETRY_DISABLED=1" \
        --set-secrets "DATABASE_URL=DATABASE_URL:latest,NEXTAUTH_SECRET=NEXTAUTH_SECRET:latest,JWT_SECRET=JWT_SECRET:latest,GOOGLE_CLIENT_ID=GOOGLE_CLIENT_ID:latest,GOOGLE_CLIENT_SECRET=GOOGLE_CLIENT_SECRET:latest"
    
    print_success "Deployment completed successfully"
}

run_migrations() {
    print_step "Running database migrations..."
    
    # Get the service URL
    SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --region=$REGION --format="value(status.url)")
    
    print_warning "You may need to run database migrations manually:"
    echo "1. Connect to your database"
    echo "2. Run: npx prisma migrate deploy"
    echo "3. Or use Cloud Run Jobs to run migrations"
    
    print_success "Migration instructions provided"
}

show_deployment_info() {
    print_step "Deployment Information"
    
    SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --region=$REGION --format="value(status.url)")
    
    echo -e "${GREEN}🎉 Deployment completed successfully!${NC}"
    echo
    echo "Service URL: $SERVICE_URL"
    echo "Project ID: $PROJECT_ID"
    echo "Region: $REGION"
    echo "Service Name: $SERVICE_NAME"
    echo
    echo "Next steps:"
    echo "1. Update your NEXTAUTH_URL environment variable to: $SERVICE_URL"
    echo "2. Configure your Google OAuth redirect URIs to include: $SERVICE_URL/api/auth/callback/google"
    echo "3. Test your application at: $SERVICE_URL"
    echo "4. Monitor logs with: gcloud logs tail --service=$SERVICE_NAME"
}

# Main execution
main() {
    echo -e "${BLUE}HoliTime Cloud Run Deployment${NC}"
    echo "=================================="
    
    # Check if PROJECT_ID is set
    if [ "$PROJECT_ID" = "your-project-id" ]; then
        print_error "Please set PROJECT_ID environment variable or update the script"
        echo "Example: export PROJECT_ID=my-project-id"
        exit 1
    fi
    
    check_prerequisites
    setup_project
    
    # Ask if user wants to create secrets
    echo -n "Do you want to create/update secrets? (y/n): "
    read -r create_secrets_choice
    if [[ $create_secrets_choice =~ ^[Yy]$ ]]; then
        create_secrets
    fi
    
    build_and_push_image
    deploy_to_cloud_run
    run_migrations
    show_deployment_info
}

# Run the main function
main "$@"

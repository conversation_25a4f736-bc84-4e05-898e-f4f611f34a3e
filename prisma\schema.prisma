// Complete and validated Prisma schema

datasource db {
  provider = "postgresql"
  url = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

enum UserRole {
  Staff
  Admin
  CompanyUser
  CrewChief
  Employee
}

// Temporarily using String instead of enums to avoid migration issues
// enum JobStatus {
//   Pending
//   Active
//   OnHold
//   Completed
//   Cancelled
// }

// enum ShiftStatus {
//   Pending
//   Active
//   InProgress
//   Completed
//   Cancelled
// }

// enum TimesheetStatus {
//   draft
//   pending_client_approval
//   pending_manager_approval
//   completed
//   rejected
// }

model User {
  id             String      @id @default(cuid())
  name           String
  email          String      @unique
  passwordHash   String
  role           UserRole    @default(Staff)
  avatarUrl      String?
  isActive       Boolean     @default(true)
  
  // Additional employee-specific fields
  crewChiefEligible     Boolean   @default(false)
  forkOperatorEligible  Boolean   @default(false)  
  certifications        String[]  @default([])
  performance           Float?
  location              String?
  
  // Relations
  companyId     String?
  company       Company?    @relation(fields: [companyId], references: [id])
  assignments   AssignedPersonnel[]
  notifications Notification[]
  announcements Announcement[]

  @@map("users")
}

model Company {
  id        String    @id @default(cuid())
  name      String    @unique
  address   String?
  phone     String?
  email     String?
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt

  // Relations
  users     User[]
  jobs      Job[]

  @@map("companies")
}

model AssignedPersonnel {
  id            String      @id @default(cuid())
  shift         Shift       @relation(fields: [shiftId], references: [id])
  shiftId       String
  user          User        @relation(fields: [userId], references: [id])
  userId        String
  roleCode      String      @default("WR") // WR = Worker, CC = Crew Chief
  isPlaceholder Boolean     @default(false)
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt
  
  // Relations
  timeEntries TimeEntry[]
  permissions CrewChiefPermission[]

  @@map("assigned_personnel")
}

model WorkerRequirement {
  id            String      @id @default(cuid())
  shift         Shift       @relation(fields: [shiftId], references: [id])
  shiftId       String
  roleCode      String      // 'CC', 'SH', 'FO', 'RFO', 'RG', 'GL'
  roleName      String
  requiredCount Int         @default(0)
  color         String?
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt

  @@map("worker_requirements")
  @@unique([shiftId, roleCode])
}

model TimeEntry {
  id                 String    @id @default(cuid())
  assignedPersonnel   AssignedPersonnel @relation(fields: [assignedPersonnelId], references: [id])
  assignedPersonnelId String
  clockIn            DateTime
  clockOut           DateTime?
  breakStart         DateTime?
  breakEnd           DateTime?
  notes              String?
  verified           Boolean   @default(false)
  createdAt          DateTime  @default(now())
  updatedAt          DateTime  @updatedAt

  @@map("time_entries")
}

model CrewChiefPermission {
  id            String      @id @default(cuid())
  permissionType String     // 'shift' or 'job'
  targetId      String      // shiftId or jobId
  assignedPersonnel AssignedPersonnel @relation(fields: [assignedPersonnelId], references: [id])
  assignedPersonnelId String
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt

  @@map("crew_chief_permissions")
}

model Shift {
  id                String      @id @default(cuid())
  job               Job         @relation(fields: [jobId], references: [id])
  jobId             String
  date              DateTime
  startTime         DateTime
  endTime           DateTime
  requestedWorkers  Int?
  status            String      @default("pending")
  location          String?
  description       String?
  requirements      String?
  notes             String?
  createdAt         DateTime    @default(now())
  updatedAt         DateTime    @updatedAt

  // Relations
  assignedPersonnel AssignedPersonnel[]
  timesheets       Timesheet?
  workerRequirements WorkerRequirement[]

  @@map("shifts")
}

model Job {
  id           String      @id @default(cuid())
  name         String
  description  String?
  status       String      @default("Pending")
  startDate    DateTime?
  endDate      DateTime?
  location     String?
  budget       String?
  notes        String?
  isCompleted  Boolean     @default(false)
  company      Company     @relation(fields: [companyId], references: [id])
  companyId    String
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt

  // Relations
  shifts      Shift[]
  documents   Document[]

  @@map("jobs")
  @@unique([name, companyId])
}

model Document {
  id           String      @id @default(cuid())
  name         String
  url          String
  job          Job?        @relation(fields: [jobId], references: [id])
  jobId        String?
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt

  @@map("documents")
}

model Timesheet {
  id                    String          @id @default(cuid())
  shift                 Shift           @relation(fields: [shiftId], references: [id])
  shiftId               String          @unique
  status                String          @default("draft")
  submittedAt           DateTime?
  submittedBy           String?
  clientApprovedAt      DateTime?
  clientApprovedBy      String?
  clientSignature       String?
  managerApprovedAt     DateTime?
  managerApprovedBy     String?
  managerSignature      String?
  rejectedAt            DateTime?
  rejectionReason       String?
  notes                 String?
  createdAt             DateTime        @default(now())
  updatedAt             DateTime        @updatedAt

  @@map("timesheets")
}

model Notification {
  id                  String      @id @default(cuid())
  user                User        @relation(fields: [userId], references: [id])
  userId              String
  type                String      // 'timesheet_ready_for_approval', 'timesheet_approved', etc.
  title               String
  message             String
  relatedTimesheetId  String?
  relatedShiftId      String?
  isRead              Boolean     @default(false)
  createdAt           DateTime    @default(now())
  updatedAt           DateTime    @updatedAt

  @@map("notifications")
}

model Announcement {
  id          String      @id @default(cuid())
  title       String
  content     String
  date        DateTime    @default(now())
  createdBy   User        @relation(fields: [createdById], references: [id])
  createdById String
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  @@map("announcements")
}

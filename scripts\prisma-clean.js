const fs = require('fs');
const path = require('path');

try {
  const prismaClientPath = path.join('node_modules', '.prisma', 'client');
  if (fs.existsSync(prismaClientPath)) {
    fs.rmSync(prismaClientPath, { recursive: true, force: true });
    console.log('Successfully removed Prisma client directory');
  } else {
    console.log('Prisma client directory not found');
  }
} catch (err) {
  console.error('Error cleaning Prisma client:', err);
}

# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules*
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

.genkit/*
# Environment variables
# Ignore all .env files except the example template

!.env.example
!.env
!.env.development
!.env.production

firebase*
firestore-debug.log




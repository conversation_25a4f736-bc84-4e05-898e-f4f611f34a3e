/*
  Warnings:

  - The values [Manager] on the enum `UserRole` will be removed. If these variants are still used in the database, this will fail.
  - You are about to drop the column `notes` on the `companies` table. All the data in the column will be lost.
  - You are about to drop the column `createdAt` on the `users` table. All the data in the column will be lost.
  - You are about to drop the column `lastLogin` on the `users` table. All the data in the column will be lost.
  - You are about to drop the column `updatedAt` on the `users` table. All the data in the column will be lost.
  - You are about to drop the `assigned_personnel` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `jobs` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `password_reset_tokens` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `shifts` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `time_entries` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `timesheets` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `worker_requirements` table. If the table is not empty, all the data it contains will be lost.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "UserRole_new" AS ENUM ('Staff', 'Admin', 'CompanyUser', 'CrewChief', 'Employee');
ALTER TABLE "users" ALTER COLUMN "role" DROP DEFAULT;
ALTER TABLE "users" ALTER COLUMN "role" TYPE "UserRole_new" USING ("role"::text::"UserRole_new");
ALTER TYPE "UserRole" RENAME TO "UserRole_old";
ALTER TYPE "UserRole_new" RENAME TO "UserRole";
DROP TYPE "UserRole_old";
ALTER TABLE "users" ALTER COLUMN "role" SET DEFAULT 'Staff';
COMMIT;

-- DropForeignKey
ALTER TABLE "assigned_personnel" DROP CONSTRAINT "assigned_personnel_shiftId_fkey";

-- DropForeignKey
ALTER TABLE "assigned_personnel" DROP CONSTRAINT "assigned_personnel_userId_fkey";

-- DropForeignKey
ALTER TABLE "jobs" DROP CONSTRAINT "jobs_companyId_fkey";

-- DropForeignKey
ALTER TABLE "password_reset_tokens" DROP CONSTRAINT "password_reset_tokens_userId_fkey";

-- DropForeignKey
ALTER TABLE "shifts" DROP CONSTRAINT "shifts_jobId_fkey";

-- DropForeignKey
ALTER TABLE "time_entries" DROP CONSTRAINT "time_entries_assignedPersonnelId_fkey";

-- DropForeignKey
ALTER TABLE "timesheets" DROP CONSTRAINT "timesheets_shiftId_fkey";

-- DropForeignKey
ALTER TABLE "users" DROP CONSTRAINT "users_companyId_fkey";

-- DropForeignKey
ALTER TABLE "worker_requirements" DROP CONSTRAINT "worker_requirements_shiftId_fkey";

-- DropIndex
DROP INDEX "users_companyId_idx";

-- DropIndex
DROP INDEX "users_role_idx";

-- AlterTable
ALTER TABLE "companies" DROP COLUMN "notes";

-- AlterTable
ALTER TABLE "users" DROP COLUMN "createdAt",
DROP COLUMN "lastLogin",
DROP COLUMN "updatedAt",
ADD COLUMN     "crewChiefEligible" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "forkOperatorEligible" BOOLEAN NOT NULL DEFAULT false;

-- DropTable
DROP TABLE "assigned_personnel";

-- DropTable
DROP TABLE "jobs";

-- DropTable
DROP TABLE "password_reset_tokens";

-- DropTable
DROP TABLE "shifts";

-- DropTable
DROP TABLE "time_entries";

-- DropTable
DROP TABLE "timesheets";

-- DropTable
DROP TABLE "worker_requirements";

-- DropEnum
DROP TYPE "JobStatus";

-- DropEnum
DROP TYPE "ShiftStatus";

-- DropEnum
DROP TYPE "TimesheetStatus";

-- AddForeignKey
ALTER TABLE "users" ADD CONSTRAINT "users_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "companies"("id") ON DELETE SET NULL ON UPDATE CASCADE;

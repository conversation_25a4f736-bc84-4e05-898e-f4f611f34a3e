'use client';

import { useSession } from 'next-auth/react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { AlertTriangle } from 'lucide-react';
import { CascadeDeleteDialog } from './cascade-delete-dialog';
import { UserRole } from '@prisma/client';

interface DangerZoneProps {
  entityType: 'client' | 'job' | 'shift';
  entityId: string;
  entityName: string;
  onSuccess?: () => void;
  redirectTo?: string;
  className?: string;
}

export function DangerZone({
  entityType,
  entityId,
  entityName,
  onSuccess,
  redirectTo,
  className
}: DangerZoneProps) {
  const { data: session } = useSession();

  if (session?.user?.role !== UserRole.Admin) {
    return null;
  }

  const getEntityTypeLabel = () => {
    switch (entityType) {
      case 'client':
        return 'Client Company';
      case 'job':
        return 'Job';
      case 'shift':
        return 'Shift';
      default:
        return 'Entity';
    }
  };

  const getDescription = () => {
    switch (entityType) {
      case 'client':
        return 'Permanently delete this client company and all associated jobs, shifts, and data. This action affects the most data and cannot be undone.';
      case 'job':
        return 'Permanently delete this job and all associated shifts and data. This action cannot be undone.';
      case 'shift':
        return 'Permanently delete this shift and all associated data. This action cannot be undone.';
      default:
        return 'Permanently delete this entity and all associated data.';
    }
  };

  return (
    <Card className={`border-red-400 ${className}`}>
      <CardHeader>
        <div className="flex items-center gap-2">
          <AlertTriangle className="text-red-600" />
          <CardTitle className="text-red-600">Danger Zone</CardTitle>
        </div>
        <CardDescription>
          {getDescription()}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between">
          <div>
            <p className="font-medium">Delete {getEntityTypeLabel()}</p>
            <p className="text-sm text-muted-foreground">
              Once you delete this {getEntityTypeLabel().toLowerCase()}, there is no going back.
            </p>
          </div>
          <CascadeDeleteDialog
            entityType={entityType}
            entityId={entityId}
            entityName={entityName}
            onSuccess={onSuccess}
            redirectTo={redirectTo}
          />
        </div>
      </CardContent>
    </Card>
  );
}

'use client'

import { useState, useRef, useEffect } from 'react'
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'

import { Separator } from '@/components/ui/separator'
import { RotateCcw, Check, X } from 'lucide-react'

interface SignatureCaptureModalProps {
  isOpen: boolean
  onClose: () => void
  onSignatureSubmit: (signatureData: string) => void
  title?: string
  description?: string
  loading?: boolean
}

export function SignatureCaptureModal({
  isOpen,
  onClose,
  onSignatureSubmit,
  title = "Digital Signature",
  description = "Please sign below to approve this timesheet",
  loading = false
}: SignatureCaptureModalProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [isDrawing, setIsDrawing] = useState(false)
  const [hasSignature, setHasSignature] = useState(false)
  const [lastPoint, setLastPoint] = useState<{ x: number; y: number } | null>(null)

  const clearSignature = () => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (ctx) {
      ctx.clearRect(0, 0, canvas.width, canvas.height)
      setHasSignature(false)
    }
  }

  const handleClose = () => {
    clearSignature()
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <p className="text-sm text-muted-foreground">{description}</p>
        </DialogHeader>

        <div className="space-y-4">
          <div className="border-2 border-solid border-gray-300 rounded-lg p-4 bg-white">
            <canvas
              ref={canvasRef}
              className="w-full h-48 cursor-crosshair touch-none"
            />
          </div>

          <div className="text-sm text-muted-foreground text-center">
            <p>Sign above using your mouse or finger on touch devices</p>
          </div>

          <Separator />

          <div className="flex justify-between">
            <Button variant="outline" onClick={clearSignature}>
              Clear
            </Button>
            <div className="flex gap-2">
              <Button variant="outline" onClick={handleClose}>
                Cancel
              </Button>
              <Button onClick={() => onSignatureSubmit('')}>
                Submit Signature
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default SignatureCaptureModal

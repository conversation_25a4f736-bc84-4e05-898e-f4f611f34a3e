import { describe, it, expect } from '@jest/globals';
import {
  userValidation,
  companyValidation,
  jobValidation,
  shiftValidation,
  timesheetValidation,
  notificationValidation,
  announcementValidation,
  workerRequirementValidation,
  timeEntryValidation,
  queryValidation,
  validateRequest,
} from '../validation';

describe('Validation Schemas', () => {
  describe('userValidation', () => {
    describe('create', () => {
      it('should validate valid user creation data', () => {
        const validData = {
          name: '<PERSON>',
          email: '<EMAIL>',
          password: 'password123',
          role: 'Employee' as const,
          avatarUrl: 'https://example.com/avatar.jpg',
        };

        const result = userValidation.create.safeParse(validData);
        expect(result.success).toBe(true);
      });

      it('should reject invalid email', () => {
        const invalidData = {
          name: '<PERSON>',
          email: 'invalid-email',
          password: 'password123',
          role: 'Employee' as const,
        };

        const result = userValidation.create.safeParse(invalidData);
        expect(result.success).toBe(false);
      });

      it('should reject short password', () => {
        const invalidData = {
          name: '<PERSON>e',
          email: '<EMAIL>',
          password: '123',
          role: 'Employee' as const,
        };

        const result = userValidation.create.safeParse(invalidData);
        expect(result.success).toBe(false);
      });
    });

    describe('update', () => {
      it('should validate partial user update data', () => {
        const validData = {
          name: 'Jane Doe',
          performance: 85,
        };

        const result = userValidation.update.safeParse(validData);
        expect(result.success).toBe(true);
      });
    });
  });

  describe('companyValidation', () => {
    it('should validate valid company data', () => {
      const validData = {
        name: 'Acme Corp',
        address: '123 Main St',
        phone: '+1234567890',
        email: '<EMAIL>',
      };

      const result = companyValidation.create.safeParse(validData);
      expect(result.success).toBe(true);
    });

    it('should reject invalid phone number', () => {
      const invalidData = {
        name: 'Acme Corp',
        phone: 'invalid-phone',
      };

      const result = companyValidation.create.safeParse(invalidData);
      expect(result.success).toBe(false);
    });
  });

  describe('jobValidation', () => {
    it('should validate valid job creation data', () => {
      const validData = {
        name: 'Construction Project',
        description: 'Building construction',
        companyId: 'clh1234567890123456789',
        status: 'Active' as const,
        startDate: '2024-01-01T00:00:00Z',
        location: 'Downtown',
      };

      const result = jobValidation.create.safeParse(validData);
      expect(result.success).toBe(true);
    });

    it('should reject invalid status', () => {
      const invalidData = {
        name: 'Construction Project',
        companyId: 'clh1234567890123456789',
        status: 'InvalidStatus',
      };

      const result = jobValidation.create.safeParse(invalidData);
      expect(result.success).toBe(false);
    });
  });

  describe('shiftValidation', () => {
    it('should validate valid shift data', () => {
      const validData = {
        jobId: 'clh1234567890123456789',
        date: '2024-01-01T00:00:00Z',
        startTime: '2024-01-01T08:00:00Z',
        endTime: '2024-01-01T17:00:00Z',
        requestedWorkers: 5,
        status: 'Pending' as const,
      };

      const result = shiftValidation.create.safeParse(validData);
      expect(result.success).toBe(true);
    });

    it('should reject negative worker count', () => {
      const invalidData = {
        jobId: 'clh1234567890123456789',
        date: '2024-01-01T00:00:00Z',
        startTime: '2024-01-01T08:00:00Z',
        endTime: '2024-01-01T17:00:00Z',
        requestedWorkers: -1,
      };

      const result = shiftValidation.create.safeParse(invalidData);
      expect(result.success).toBe(false);
    });
  });

  describe('timesheetValidation', () => {
    it('should validate timesheet approval data', () => {
      const validData = {
        signature: 'John Doe',
        notes: 'Approved',
        approvalType: 'manager' as const,
      };

      const result = timesheetValidation.approve.safeParse(validData);
      expect(result.success).toBe(true);
    });

    it('should validate timesheet rejection data', () => {
      const validData = {
        reason: 'Incomplete hours',
        notes: 'Please resubmit with correct hours',
      };

      const result = timesheetValidation.reject.safeParse(validData);
      expect(result.success).toBe(true);
    });

    it('should reject rejection without reason', () => {
      const invalidData = {
        notes: 'Please resubmit',
      };

      const result = timesheetValidation.reject.safeParse(invalidData);
      expect(result.success).toBe(false);
    });
  });

  describe('workerRequirementValidation', () => {
    it('should validate valid worker requirement data', () => {
      const validData = {
        shiftId: 'clh1234567890123456789',
        roleCode: 'CC' as const,
        roleName: 'Crew Chief',
        requiredCount: 2,
        color: '#FF0000',
      };

      const result = workerRequirementValidation.create.safeParse(validData);
      expect(result.success).toBe(true);
    });

    it('should reject invalid role code', () => {
      const invalidData = {
        shiftId: 'clh1234567890123456789',
        roleCode: 'INVALID',
        roleName: 'Invalid Role',
        requiredCount: 1,
      };

      const result = workerRequirementValidation.create.safeParse(invalidData);
      expect(result.success).toBe(false);
    });
  });

  describe('queryValidation', () => {
    it('should validate pagination parameters', () => {
      const validData = {
        limit: '20',
        offset: '0',
      };

      const result = queryValidation.pagination.safeParse(validData);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.limit).toBe(20);
        expect(result.data.offset).toBe(0);
      }
    });

    it('should apply default values for pagination', () => {
      const result = queryValidation.pagination.safeParse({});
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.limit).toBe(20);
        expect(result.data.offset).toBe(0);
      }
    });

    it('should reject invalid pagination values', () => {
      const invalidData = {
        limit: '0',
        offset: '-1',
      };

      const result = queryValidation.pagination.safeParse(invalidData);
      expect(result.success).toBe(false);
    });
  });

  describe('validateRequest helper', () => {
    it('should return success for valid data', () => {
      const schema = userValidation.create;
      const validData = {
        name: 'John Doe',
        email: '<EMAIL>',
        password: 'password123',
        role: 'Employee' as const,
      };

      const result = validateRequest(schema, validData);
      expect(result.success).toBe(true);
    });

    it('should return error for invalid data', () => {
      const schema = userValidation.create;
      const invalidData = {
        name: '',
        email: 'invalid-email',
        password: '123',
      };

      const result = validateRequest(schema, invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.message).toBe('Invalid request data');
        expect(result.error.issues).toBeDefined();
      }
    });
  });
});

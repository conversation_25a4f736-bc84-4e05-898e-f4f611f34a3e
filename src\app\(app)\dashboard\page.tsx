'use client';

import { useUser } from '@/hooks/use-user';
import ClientDashboard from '../(dashboards)/client/page';
import EmployeeDashboard from '../(dashboards)/employee/page';
import CrewChiefDashboard from '../(dashboards)/crew-chief/page';
import ManagerDashboard from '../(dashboards)/manager/page';

export default function DashboardPage() {
  const { user } = useUser();

  if (!user) {
    return <div>Loading...</div>;
  }

  switch (user.role) {
    case 'CompanyUser':
      return <ClientDashboard />;
    case 'Staff':
      return <EmployeeDashboard />;
    case 'CrewChief':
      return <CrewChiefDashboard />;
    case 'Admin':
      return <ManagerDashboard />;
    case 'Admin':
      return <ManagerDashboard />;
    default:
      return <div>Welcome!</div>;
  }
}
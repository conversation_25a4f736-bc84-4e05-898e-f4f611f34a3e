import { NextRequest, NextResponse } from 'next/server';
import { createUser, getUserByEmail } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, password, name, role, companyName, phone } = body;

    // Basic validation
    if (!email || !password || !name) {
      return NextResponse.json(
        { error: 'Email, password, and name are required' },
        { status: 400 }
      );
    }

    // Role validation
    const validRoles = ['Employee', 'Crew Chief', 'Admin', 'Customer', 'User'];
    const finalRole = role && validRoles.includes(role) ? role : 'User';

    // Check if user already exists
    try {
      await getUserByEmail(email);
      return NextResponse.json(
        { error: 'User with this email already exists' },
        { status: 409 }
      );
    } catch (error) {
      // This is expected if the user does not exist, so we can continue.
    }

    // Create new user
    const user = await createUser({
      email,
      password,
      name,
      role: finalRole,
      companyName,
      phone,
    });

    return NextResponse.json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        image: user.image,
      },
    });
  } catch (error) {
    console.error('Registration error:', error);
    const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
    return NextResponse.json(
      { error: 'Internal server error', details: errorMessage },
      { status: 500 }
    );
  }
}

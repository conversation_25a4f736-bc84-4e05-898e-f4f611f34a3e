import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    // Get all shifts with their company and job names for debugging
    const result = await prisma.$queryRaw`
      SELECT
        s.id,
        s.date,
        c.name as company_name,
        j.name as job_name,
        s.location,
        s.status
      FROM shifts s
      JOIN jobs j ON s."jobId" = j.id
      JOIN companies c ON j."companyId" = c.id
      ORDER BY s.date DESC
    `

    return NextResponse.json({
      success: true,
      shifts: result,
      count: result.length
    })

  } catch (error) {
    console.error('Error getting debug shifts:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

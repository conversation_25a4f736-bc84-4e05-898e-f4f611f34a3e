import { PrismaClient } from '@prisma/client';
import { parse } from 'csv-parse/sync';
import fs from 'fs';

const prisma = new PrismaClient();

interface ShiftRecord {
  clientName: string;
  jobName: string;
  shiftDate: string;
  shiftStart: string;
  shiftEnd: string;
  workerName: string;
}

async function main() {
  try {
    const csvData = fs.readFileSync('test_shifts.csv', 'utf8');

    const records: ShiftRecord[] = parse(csvData, {
      columns: ['clientName', 'jobName', 'shiftDate', 'shiftStart', 'shiftEnd', 'workerName'],
      skip_empty_lines: true,
      from_line: 2
    });

    for (const record of records) {
      try {
        // Create company
        const company = await prisma.company.upsert({
          where: { name: record.clientName },
          create: {
            name: record.clientName,
          },
          update: {}
        });

        // Create job
        const job = await prisma.job.upsert({
          where: {
            name_companyId: {
              name: record.jobName,
              companyId: company.id
            }
          },
          create: {
            name: record.jobName,
            companyId: company.id,
            status: 'InProgress',
            description: ''
          },
          update: {}
        });

        console.log(`Successfully imported shift for ${record.clientName}`);
      } catch (err) {
        console.error(`Error importing shift:`, err);
      }
    }
  } catch (err) {
    console.error('Failed to import shifts:', err);
  } finally {
    await prisma.$disconnect();
  }
}

main();

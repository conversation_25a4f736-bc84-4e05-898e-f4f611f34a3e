# HoliTime Cloud Run Deployment Guide

This guide will walk you through deploying the HoliTime application to Google Cloud Run.

## Prerequisites

Before you begin, ensure you have:

1. **Google Cloud Account** with billing enabled
2. **Google Cloud SDK (gcloud)** installed and configured
3. **Docker** installed and running
4. **Node.js 18+** installed
5. **A PostgreSQL database** (Cloud SQL recommended)

## Quick Start

### 1. Install Google Cloud SDK

If you haven't already, install the Google Cloud SDK:

**Windows:**
```powershell
# Download and run the installer from:
# https://cloud.google.com/sdk/docs/install-sdk#windows
```

**macOS:**
```bash
brew install --cask google-cloud-sdk
```

**Linux:**
```bash
curl https://sdk.cloud.google.com | bash
exec -l $SHELL
```

### 2. Authenticate and Setup

```bash
# Login to Google Cloud
gcloud auth login

# Set your project ID (replace with your actual project ID)
export PROJECT_ID="elated-fabric-460119-t3"
gcloud config set project $PROJECT_ID

# Enable required APIs
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable containerregistry.googleapis.com
gcloud services enable secretmanager.googleapis.com
gcloud services enable sqladmin.googleapis.com
```

### 3. Setup Database (Cloud SQL)

Create a PostgreSQL instance:

```bash
# Create Cloud SQL instance
gcloud sql instances create holitime-db \
    --database-version=POSTGRES_14 \
    --tier=db-f1-micro \
    --region=us-central1

# Create database
gcloud sql databases create holitime --instance=holitime-db

# Create user
gcloud sql users create holitime-user \
    --instance=holitime-db \
    --password=your-secure-password
```

Get your database connection string:
```bash
gcloud sql instances describe holitime-db --format="value(connectionName)"
```

Your DATABASE_URL will be:
```
******************************************************************************************************
```

### 4. Create Secrets

Store your environment variables securely in Secret Manager:

```bash
# Database URL
echo -n "******************************************************************************************" | \
gcloud secrets create DATABASE_URL --data-file=-

# NextAuth Secret (generate a secure random string)
openssl rand -base64 32 | gcloud secrets create NEXTAUTH_SECRET --data-file=-

# JWT Secret
openssl rand -base64 32 | gcloud secrets create JWT_SECRET --data-file=-

# Google OAuth credentials (from Google Cloud Console)
echo -n "your-google-client-id" | gcloud secrets create GOOGLE_CLIENT_ID --data-file=-
echo -n "your-google-client-secret" | gcloud secrets create GOOGLE_CLIENT_SECRET --data-file=-
```

### 5. Deploy Using Script

**Option A: PowerShell (Windows)**
```powershell
.\scripts\deploy-cloud-run.ps1 -ProjectId "your-project-id"
```

**Option B: Bash (Linux/macOS)**
```bash
export PROJECT_ID="your-project-id"
./scripts/deploy-cloud-run.sh
```

**Option C: Manual Deployment**
```bash
# Build and push image
docker build -t gcr.io/$PROJECT_ID/holitime:latest .
docker push gcr.io/$PROJECT_ID/holitime:latest

# Deploy to Cloud Run
gcloud run deploy holitime \
    --image gcr.io/$PROJECT_ID/holitime:latest \
    --region us-central1 \
    --platform managed \
    --allow-unauthenticated \
    --port 3000 \
    --memory 2Gi \
    --cpu 2 \
    --max-instances 10 \
    --min-instances 1 \
    --concurrency 80 \
    --timeout 300 \
    --set-env-vars "NODE_ENV=production,NEXT_TELEMETRY_DISABLED=1" \
    --set-secrets "DATABASE_URL=DATABASE_URL:latest,NEXTAUTH_SECRET=NEXTAUTH_SECRET:latest,JWT_SECRET=JWT_SECRET:latest,GOOGLE_CLIENT_ID=GOOGLE_CLIENT_ID:latest,GOOGLE_CLIENT_SECRET=GOOGLE_CLIENT_SECRET:latest"
```

## Post-Deployment Setup

### 1. Run Database Migrations

After deployment, run the database migrations:

```bash
# Get your service URL
SERVICE_URL=$(gcloud run services describe holitime --region=us-central1 --format="value(status.url)")

# Option A: Run migrations locally (if you have database access)
DATABASE_URL="your-database-url" npx prisma migrate deploy

# Option B: Use Cloud Run Jobs (recommended for production)
gcloud run jobs create holitime-migrate \
    --image gcr.io/$PROJECT_ID/holitime:latest \
    --region us-central1 \
    --set-secrets "DATABASE_URL=DATABASE_URL:latest" \
    --command "npx" \
    --args "prisma,migrate,deploy"

# Run the migration job
gcloud run jobs execute holitime-migrate --region us-central1
```

### 2. Update OAuth Configuration

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Navigate to APIs & Services > Credentials
3. Edit your OAuth 2.0 client
4. Add authorized redirect URI: `https://your-service-url/api/auth/callback/google`

### 3. Update Environment Variables

Update the NEXTAUTH_URL secret with your service URL:

```bash
SERVICE_URL=$(gcloud run services describe holitime --region=us-central1 --format="value(status.url)")
echo -n "$SERVICE_URL" | gcloud secrets create NEXTAUTH_URL --data-file=-

# Update the Cloud Run service to use the new secret
gcloud run services update holitime \
    --region us-central1 \
    --update-secrets "NEXTAUTH_URL=NEXTAUTH_URL:latest"
```

## Monitoring and Maintenance

### View Logs
```bash
# Real-time logs
gcloud logs tail --service=holitime

# Historical logs
gcloud logs read --service=holitime --limit=50
```

### Update Deployment
```bash
# Rebuild and redeploy
docker build -t gcr.io/$PROJECT_ID/holitime:latest .
docker push gcr.io/$PROJECT_ID/holitime:latest

gcloud run services update holitime \
    --image gcr.io/$PROJECT_ID/holitime:latest \
    --region us-central1
```

### Scale Service
```bash
# Update scaling settings
gcloud run services update holitime \
    --region us-central1 \
    --min-instances 2 \
    --max-instances 20
```

## Troubleshooting

### Common Issues

**1. Build Failures**
- Check Docker is running
- Verify all dependencies are installed
- Check for TypeScript errors: `npm run build`

**2. Database Connection Issues**
- Verify Cloud SQL instance is running
- Check database credentials in secrets
- Ensure Cloud SQL Auth Proxy is configured correctly

**3. Authentication Issues**
- Verify NEXTAUTH_SECRET is set
- Check Google OAuth configuration
- Ensure NEXTAUTH_URL matches your service URL

**4. Permission Issues**
- Verify service account has necessary permissions
- Check IAM roles for Cloud Run, Secret Manager, and Cloud SQL

### Debug Commands
```bash
# Check service status
gcloud run services describe holitime --region us-central1

# View service configuration
gcloud run services describe holitime --region us-central1 --format export

# Test database connection
gcloud sql connect holitime-db --user=holitime-user

# Check secrets
gcloud secrets versions list DATABASE_URL
```

## Security Best Practices

1. **Use least privilege IAM roles**
2. **Regularly rotate secrets**
3. **Enable VPC connector for database access**
4. **Use Cloud Armor for DDoS protection**
5. **Enable audit logging**
6. **Set up monitoring and alerting**

## Cost Optimization

1. **Set appropriate min/max instances**
2. **Use CPU allocation efficiently**
3. **Monitor and adjust memory allocation**
4. **Use Cloud SQL proxy for connection pooling**
5. **Implement caching strategies**

## Backup and Recovery

```bash
# Database backup
gcloud sql backups create --instance=holitime-db

# Export database
gcloud sql export sql holitime-db gs://your-backup-bucket/backup.sql \
    --database=holitime
```

For more detailed information, refer to the [Google Cloud Run documentation](https://cloud.google.com/run/docs).
